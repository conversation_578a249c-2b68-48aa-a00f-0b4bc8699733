from flask import Flask, render_template, redirect, url_for, request, send_from_directory, abort, make_response, jsonify
from flask_compress import Compress
import os
import time
import json
from functools import wraps, lru_cache
from services.media_service import MediaService
from services.thumbnail_service import init_thumbnail_service, get_thumbnail_service
from models.creator import Creator

app = Flask(__name__)
app.config.from_object('config')
app.config['COMPRESS_ALGORITHM'] = 'gzip'  # oder 'brotli' falls verfügbar
app.config['COMPRESS_LEVEL'] = 6
app.config['COMPRESS_MIN_SIZE'] = 500
app.config['COMPRESS_MIMETYPES'] = [
    'text/html', 'text/css', 'text/xml',
    'application/json', 'application/javascript',
    'image/svg+xml', 'application/font-woff2'
]

# Kompression aktivieren
Compress(app)

# Thumbnail-Service temporär deaktiviert für Debugging
thumbnail_service = None
print("⚠️  Thumbnail-Service deaktiviert für Debugging")

def cache_header(max_age, **cache_kwargs):
    """Fügt Cache-Control Header zur Antwort hinzu"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            response = make_response(f(*args, **kwargs))
            if max_age == 0:
                response.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
                response.headers['Pragma'] = 'no-cache'
                response.headers['Expires'] = '0'
            else:
                response.headers['Cache-Control'] = f'public, max-age={max_age}'
            return response
        return decorated_function
    return decorator

# MediaService wird in den Routen-Funktionen initialisiert

@app.route('/')
@cache_header(max_age=300)  # 5 Minuten Cache
@lru_cache(maxsize=128)  # In-Memory Cache für häufige Anfragen
def index():
    """Desktop-Ansicht - zeigt Creator in einer Kachelkarten-Anordnung"""
    media_service = MediaService(app.config)
    creators = media_service.get_all_creators()
    response = make_response(render_template('desktop.html', creators=creators))
    return response

@app.route('/mobile/debug')
def mobile_debug():
    """Debug-Seite für mobile Ansicht"""
    return render_template('mobile_debug.html')

@app.route('/mobile/simple')
def mobile_simple():
    """Vereinfachte mobile Ansicht für bessere Performance"""
    return render_template('mobile_simple.html')

@app.route('/mobile/ultrafast')
def mobile_ultrafast():
    """Ultra-Fast mobile Ansicht für extrem flüssiges Swipen"""
    return render_template('mobile_ultrafast.html')

@app.route('/performance')
def performance_comparison():
    """Performance-Vergleichsseite"""
    return render_template('performance_comparison.html')

@app.route('/mobile')
@cache_header(max_age=300)  # 5 Minuten Cache für die Startseite
def mobile():
    """Mobile-Ansicht - zeigt eine TikTok-ähnliche vertikale Swipe-Oberfläche"""
    try:
        # Initial nur Metadaten laden, keine Medien
        media_service = MediaService(app.config)

        # Ersten Batch mit nur 5 Einträgen laden für schnelleren Start
        result = media_service.get_media_batch(offset=0, limit=5)

        # Standardwerte setzen
        batch = {
            'items': [],
            'total': 0,
            'limit': 5,  # Reduzierte Batch-Größe
            'offset': 0,
            'has_more': False
        }

        # Daten aus dem Ergebnis extrahieren
        if result.get('success') and 'data' in result:
            batch.update(result['data'])

            # Nur notwendige Metadaten behalten
            for item in batch['items']:
                item.pop('full_path', None)  # Große Daten entfernen
                item.pop('thumbnail', None)  # Thumbnails werden später geladen

        # Template mit minimalen Daten rendern
        response = make_response(render_template('mobile.html', media_batch=batch))

        # Browser-Caching Header setzen
        response.headers['Cache-Control'] = 'public, max-age=300'  # 5 Minuten
        return response

    except Exception as e:
        app.logger.error(f"Fehler in der mobile()-Funktion: {str(e)}")
        app.logger.exception("Ausführliche Fehlerinformationen:")
        return "Ein Fehler ist aufgetreten. Bitte versuchen Sie es später erneut.", 500

@app.route('/api/media/batch')
@cache_header(max_age=300)  # 5 Minuten Cache für bessere Performance
def get_media_batch():
    """API-Endpunkt zum Laden eines Batches von Medien - Optimiert für Performance"""
    try:
        offset = int(request.args.get('offset', 0))
        limit = min(int(request.args.get('limit', 8)), 20)  # Erhöhtes Limit für flüssiges Swipen
        fast_mode = request.args.get('fast', 'false').lower() == 'true'
        media_type = request.args.get('type', None)  # 'image', 'video', oder None für alle
        creator_filter = request.args.get('creator', None)  # Creator ID oder None für alle

        print(f"\n=== API-ANFRAGE (Fast Mode: {fast_mode}) ===")
        print(f"Offset: {offset}, Limit: {limit}")
        print(f"Media Type Filter: {media_type}")
        print(f"Creator Filter: {creator_filter}")

        start_time = time.time()

        media_service = MediaService(app.config)
        result = media_service.get_media_batch(
            offset=offset,
            limit=limit,
            creator_filter=creator_filter,
            media_type_filter=media_type,
            fast_mode=fast_mode
        )

        processing_time = (time.time() - start_time) * 1000
        print(f"Processing time: {processing_time:.2f}ms")

        # Debug-Ausgabe
        if result.get('success') and 'data' in result:
            data = result['data']
            items = data.get('items', [])
            print(f"Ergebnis: {len(items)} von {data.get('total', 0)} Medien geladen")
            print(f"Hat mehr: {data.get('has_more', False)}")

            # Ersten 2 Items anzeigen (reduziert für Performance)
            for i, item in enumerate(items[:2]):
                print(f"  Item {i+1}: {item.get('path', 'Kein Pfad')}")

            # Thumbnail-Service temporär deaktiviert
        else:
            print(f"Fehler in der API-Antwort: {result.get('error', 'Unbekannter Fehler')}")

        print("==============\n")

        # Add performance headers
        response = jsonify(result)
        response.headers['X-Processing-Time'] = f"{processing_time:.2f}ms"
        return response

    except Exception as e:
        error_msg = f"Fehler beim Laden des Medien-Batches: {str(e)}"
        print(f"\n!!! FEHLER: {error_msg}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': error_msg
        }), 500

@app.route('/api/thumbnail/stats')
@cache_header(max_age=0)  # Keine Cache für Live-Statistiken
def get_thumbnail_stats():
    """API-Endpunkt für Thumbnail-Service-Statistiken"""
    if thumbnail_service:
        stats = thumbnail_service.get_stats()
        return jsonify({
            'success': True,
            'data': stats
        })
    else:
        return jsonify({
            'success': False,
            'error': 'Thumbnail-Service nicht verfügbar'
        })

# === LIKES & BOOKMARKS API ===

def get_data_dir():
    """Erstellt und gibt das Datenverzeichnis zurück"""
    data_dir = os.path.join(app.root_path, 'data')
    if not os.path.exists(data_dir):
        os.makedirs(data_dir)
    return data_dir

def load_likes():
    """Lädt die Likes aus der JSON-Datei"""
    try:
        likes_file = os.path.join(get_data_dir(), 'likes.json')
        if os.path.exists(likes_file):
            with open(likes_file, 'r', encoding='utf-8') as f:
                return set(json.load(f))
        return set()
    except Exception as e:
        print(f"Fehler beim Laden der Likes: {e}")
        return set()

def save_likes(likes):
    """Speichert die Likes in die JSON-Datei"""
    try:
        likes_file = os.path.join(get_data_dir(), 'likes.json')
        with open(likes_file, 'w', encoding='utf-8') as f:
            json.dump(list(likes), f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"Fehler beim Speichern der Likes: {e}")
        return False

def load_bookmarks():
    """Lädt die Bookmark-Listen aus der JSON-Datei"""
    try:
        bookmarks_file = os.path.join(get_data_dir(), 'bookmarks.json')
        if os.path.exists(bookmarks_file):
            with open(bookmarks_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                # Konvertiere Listen zurück zu Sets
                for list_id, list_data in data.items():
                    if 'items' in list_data:
                        list_data['items'] = set(list_data['items'])
                return data
        return {}
    except Exception as e:
        print(f"Fehler beim Laden der Bookmarks: {e}")
        return {}

def save_bookmarks(bookmarks):
    """Speichert die Bookmark-Listen in die JSON-Datei"""
    try:
        bookmarks_file = os.path.join(get_data_dir(), 'bookmarks.json')
        # Konvertiere Sets zu Listen für JSON-Serialisierung
        data_to_save = {}
        for list_id, list_data in bookmarks.items():
            data_to_save[list_id] = {
                'name': list_data['name'],
                'items': list(list_data['items']) if isinstance(list_data['items'], set) else list_data['items']
            }

        with open(bookmarks_file, 'w', encoding='utf-8') as f:
            json.dump(data_to_save, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"Fehler beim Speichern der Bookmarks: {e}")
        return False

@app.route('/api/likes', methods=['GET'])
def get_likes():
    """API-Endpunkt zum Abrufen aller Likes"""
    try:
        likes = load_likes()
        return jsonify({
            'success': True,
            'data': list(likes)
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/likes/<media_id>', methods=['POST'])
def add_like(media_id):
    """API-Endpunkt zum Hinzufügen eines Likes"""
    try:
        likes = load_likes()
        likes.add(media_id)
        if save_likes(likes):
            return jsonify({
                'success': True,
                'message': f'Like für {media_id} hinzugefügt'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Fehler beim Speichern'
            }), 500
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/likes/<media_id>', methods=['DELETE'])
def remove_like(media_id):
    """API-Endpunkt zum Entfernen eines Likes"""
    try:
        likes = load_likes()
        likes.discard(media_id)
        if save_likes(likes):
            return jsonify({
                'success': True,
                'message': f'Like für {media_id} entfernt'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Fehler beim Speichern'
            }), 500
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/bookmarks', methods=['GET'])
def get_bookmarks():
    """API-Endpunkt zum Abrufen aller Bookmark-Listen"""
    try:
        bookmarks = load_bookmarks()
        # Konvertiere Sets zu Listen für JSON-Response
        response_data = {}
        for list_id, list_data in bookmarks.items():
            response_data[list_id] = {
                'name': list_data['name'],
                'items': list(list_data['items']) if isinstance(list_data['items'], set) else list_data['items']
            }
        return jsonify({
            'success': True,
            'data': response_data
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/bookmarks/lists', methods=['POST'])
def create_bookmark_list():
    """API-Endpunkt zum Erstellen einer neuen Bookmark-Liste"""
    try:
        data = request.get_json()
        if not data or 'name' not in data:
            return jsonify({
                'success': False,
                'error': 'Name der Liste ist erforderlich'
            }), 400

        bookmarks = load_bookmarks()
        list_id = f"list_{int(time.time() * 1000)}"  # Timestamp als ID
        bookmarks[list_id] = {
            'name': data['name'],
            'items': set()
        }

        if save_bookmarks(bookmarks):
            return jsonify({
                'success': True,
                'data': {
                    'list_id': list_id,
                    'name': data['name']
                }
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Fehler beim Speichern'
            }), 500
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/bookmarks/lists/<list_id>', methods=['DELETE'])
def delete_bookmark_list(list_id):
    """API-Endpunkt zum Löschen einer Bookmark-Liste"""
    try:
        bookmarks = load_bookmarks()
        if list_id in bookmarks:
            del bookmarks[list_id]
            if save_bookmarks(bookmarks):
                return jsonify({
                    'success': True,
                    'message': f'Liste {list_id} gelöscht'
                })
            else:
                return jsonify({
                    'success': False,
                    'error': 'Fehler beim Speichern'
                }), 500
        else:
            return jsonify({
                'success': False,
                'error': 'Liste nicht gefunden'
            }), 404
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/bookmarks/lists/<list_id>/<media_id>', methods=['POST'])
def add_to_bookmark_list(list_id, media_id):
    """API-Endpunkt zum Hinzufügen eines Mediums zu einer Bookmark-Liste"""
    try:
        bookmarks = load_bookmarks()
        if list_id not in bookmarks:
            return jsonify({
                'success': False,
                'error': 'Liste nicht gefunden'
            }), 404

        bookmarks[list_id]['items'].add(media_id)
        if save_bookmarks(bookmarks):
            return jsonify({
                'success': True,
                'message': f'Medium {media_id} zu Liste {list_id} hinzugefügt'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Fehler beim Speichern'
            }), 500
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/bookmarks/lists/<list_id>/<media_id>', methods=['DELETE'])
def remove_from_bookmark_list(list_id, media_id):
    """API-Endpunkt zum Entfernen eines Mediums aus einer Bookmark-Liste"""
    try:
        bookmarks = load_bookmarks()
        if list_id not in bookmarks:
            return jsonify({
                'success': False,
                'error': 'Liste nicht gefunden'
            }), 404

        bookmarks[list_id]['items'].discard(media_id)
        if save_bookmarks(bookmarks):
            return jsonify({
                'success': True,
                'message': f'Medium {media_id} aus Liste {list_id} entfernt'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Fehler beim Speichern'
            }), 500
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/creator/<creator_id>')
@cache_header(max_age=3600)  # 1 Stunde Cache für Creator-Details
def creator_detail(creator_id):
    """Detailseite für einen bestimmten Creator"""
    media_service = MediaService(app.config)
    creator = media_service.get_creator_by_id(creator_id)
    if not creator:
        return redirect(url_for('index'))
    response = make_response(render_template('creator_detail.html', creator=creator))
    return response

@app.context_processor
def utility_processor():
    """Hilfsfunktionen für Templates"""
    def get_random_previews(creator, count=4):
        media_service = MediaService(app.config)
        return media_service.get_random_previews(creator, count)

    def get_media_thumbnail(media_path):
        """Erzeugt oder holt ein Thumbnail für ein Medium (Bild oder Video)"""
        media_service = MediaService(app.config)
        return media_service.get_or_create_thumbnail(media_path)

    return dict(get_random_previews=get_random_previews, get_media_thumbnail=get_media_thumbnail)

@app.route('/mediacretors/<path:filename>')
@cache_header(max_age=86400 * 30)  # 30 Tage Cache für Medien
def serve_mediacretors(filename):
    """Handle legacy /mediacretors/ URLs by redirecting to /media/cretors/"""
    print(f"Redirecting legacy URL: /mediacretors/{filename} -> /media/cretors/{filename}")
    return redirect(url_for('serve_media', filename=f'cretors/{filename}'))

@app.route('/media/<path:filename>')
@cache_header(max_age=86400 * 30)  # 30 Tage Cache für Medien
def serve_media(filename):
    """
    Mediendateien und Thumbnails servieren

    Unterstützt folgende Pfade:
    - /media/thumbnails/*.jpg - Thumbnails im JPG-Format
    - /media/... - Originalmedien aus dem public-Verzeichnis
    - /mediacretors/... - Kompatibilität mit alten Pfaden
    """
    try:
        # Entferne führende Schrägstriche
        filename = filename.lstrip('/')

        # Debug-Ausgabe
        print(f"\n=== Angeforderte Datei: {filename} ===")

        # Korrigiere 'mediacretors' zu 'media/cretors'
        if filename.startswith('mediacretors/'):
            filename = filename.replace('mediacretors/', 'media/cretors/')
            print(f"Korrigierter Pfad: {filename}")

        # Sicherstellen, dass der Pfad nicht aus dem Verzeichnis ausbricht
        safe_path = os.path.normpath(filename).lstrip(os.sep)
        if '..' in safe_path or os.path.isabs(safe_path):
            print(f"Ungültiger Pfad: {safe_path}")
            abort(404)

        # Überprüfe, ob es sich um ein Thumbnail handelt
        if safe_path.startswith('thumbnails/'):
            thumb_filename = os.path.basename(safe_path)
            thumb_dir = os.path.join(app.root_path, 'static', 'thumbnails')
            os.makedirs(thumb_dir, exist_ok=True)
            thumb_path = os.path.join(thumb_dir, thumb_filename)

            if not os.path.exists(thumb_path):
                print(f"Thumbnail nicht gefunden, erstelle es neu: {thumb_path}")
                original_hash = thumb_filename.split('_')[0]

                media_dirs = [
                    os.path.join(app.root_path, 'public'),
                    os.path.join(app.root_path, 'static', 'media')
                ]

                original_path = None
                for media_dir in media_dirs:
                    for root, _, files in os.walk(media_dir):
                        for file in files:
                            # Prüfe, ob die Datei mit dem Hash im Namen beginnt
                            if file.startswith(original_hash):
                                original_path = os.path.join(root, file)
                                print(f"Gefundene Originaldatei: {original_path}")
                                break
                        if original_path:
                            break
                    if original_path:
                        break

                if original_path and os.path.exists(original_path):
                    print(f"Erstelle Thumbnail für: {original_path}")
                    from services.media_service import media_service
                    thumb_relative = media_service.get_or_create_thumbnail(original_path)
                    if thumb_relative:
                        thumb_path = os.path.join(app.root_path, 'static', thumb_relative)
                        print(f"Thumbnail erstellt: {thumb_path}")
                        return send_from_directory(os.path.dirname(thumb_path), os.path.basename(thumb_path))

                print(f"Konnte Originaldatei für Thumbnail {thumb_filename} nicht finden")
                abort(404)

            # Thumbnail existiert bereits, sende es
            return send_from_directory(thumb_dir, thumb_filename)

            print(f"Konnte Thumbnail nicht erstellen für: {original_path}")
            abort(404)

            print(f"Serviere Thumbnail: {thumb_file_path}")
            response = send_from_directory(thumb_path, thumb_filename)
            response.headers['Cache-Control'] = 'public, max-age=31536000'  # 1 Jahr Cache für Thumbnails
            return response

        # Normale Medien aus dem public-Verzeichnis laden
        public_path = os.path.join(app.root_path, 'public')
        file_path = os.path.join(public_path, safe_path)

        # Sicherstellen, dass die Datei innerhalb des public-Verzeichnisses liegt
        if not os.path.abspath(file_path).startswith(os.path.abspath(public_path)):
            print(f"Zugriff außerhalb des public-Verzeichnisses verweigert: {file_path}")
            abort(403)

        # Prüfen, ob die Datei existiert
        if not os.path.exists(file_path):
            print(f"Datei nicht gefunden: {file_path}")
            # Versuche, die Datei mit angepasstem Pfad zu finden
            alt_path = file_path.replace('public/public/', 'public/')
            if os.path.exists(alt_path):
                file_path = alt_path
                print(f"Datei unter alternativem Pfad gefunden: {file_path}")
            else:
                abort(404)

        print(f"Serviere Datei: {file_path}")

        # Datei senden
        response = send_from_directory(os.path.dirname(file_path), os.path.basename(file_path))

        # Lange Cache-Header für statische Dateien
        response.headers['Cache-Control'] = 'public, max-age=86400'  # 1 Tag Cache
        return response

    except Exception as e:
        print(f"Fehler beim Servieren der Datei {filename}: {e}")
        import traceback
        traceback.print_exc()
        abort(404)

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5001, debug=True)
