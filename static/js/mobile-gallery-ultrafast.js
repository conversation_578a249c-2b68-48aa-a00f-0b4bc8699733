/**
 * Ultra-Fast Mobile Gallery für extrem flüssiges Swipen
 * Lädt große Batches im Voraus und hält immer genug Bilder bereit
 */

// Globale Variablen
window.totalLoaded = 0;
window.totalItems = 0;
window.isLoading = false;
window.currentIndex = 0;
window.preloadBuffer = 15; // Halte immer 15 Bilder voraus bereit

// Interface State
window.interfaceState = {
    mediaTypeFilter: 'all', // 'all', 'image', 'video'
    creatorFilter: null, // null für alle, oder creator_id
    likedItems: new Set(), // Set von Media-IDs
    bookmarkLists: {}, // Object mit Listen: { "listId": { name: "Name", items: Set() } }
    infoVisible: false,
    currentMediaData: null,
    currentView: 'all', // 'all', 'likes', 'bookmarks', 'list:listId'
    navMenuVisible: false,
    bookmarkModalVisible: false
};

// Ultra-Fast Media-Laden mit großen Batches
async function loadMoreMediaUltraFast(limit = 15) {
    console.log(`🚀 Ultra-fast loading ${limit} items...`);

    if (window.isLoading) {
        console.log('⏳ Already loading...');
        return;
    }

    if (window.isReloading) {
        console.log('⏳ Currently reloading...');
        return;
    }

    window.isLoading = true;

    try {
        // Für spezielle Views (Likes, Bookmarks) verwende lokale Daten
        if (window.interfaceState.currentView === 'likes') {
            return loadLikedMedia(limit);
        } else if (window.interfaceState.currentView.startsWith('list:')) {
            const listId = window.interfaceState.currentView.substring(5);
            return loadBookmarkListMedia(listId, limit);
        }

        const offset = window.totalLoaded || 0;

        // Verhindere doppelte Anfragen mit gleichem Offset
        if (window.lastLoadedOffset === offset && offset > 0) {
            console.log(`⚠️ Skipping duplicate request for offset ${offset}`);
            window.isLoading = false;
            return;
        }

        let url = `/api/media/batch?offset=${offset}&limit=${limit}&fast=true`;

        // Filter hinzufügen
        if (window.interfaceState.mediaTypeFilter !== 'all') {
            url += `&type=${window.interfaceState.mediaTypeFilter}`;
        }
        if (window.interfaceState.creatorFilter) {
            url += `&creator=${window.interfaceState.creatorFilter}`;
        }

        console.log(`🌐 Fetching: ${url}`);

        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }

        const result = await response.json();
        console.log(`📦 Loaded ${result.data.items.length} items`);

        if (!result.success || !result.data || !result.data.items) {
            throw new Error('Invalid API response');
        }

        const container = document.querySelector('.swipe-container');
        if (!container) {
            throw new Error('Container not found');
        }

        const items = result.data.items;

        // Erstelle alle Media-Elemente parallel
        const promises = items.map((item, index) => {
            return createUltraFastMediaElement(item, window.totalLoaded + index);
        });

        const mediaElements = await Promise.all(promises);

        // Füge alle Elemente gleichzeitig hinzu
        mediaElements.forEach(element => {
            if (element) {
                container.appendChild(element);
            }
        });

        // Update counters
        window.totalLoaded += items.length;
        window.totalItems = result.data.total;
        window.lastLoadedOffset = offset; // Merke letzten Offset

        console.log(`📊 Total: ${window.totalLoaded}/${window.totalItems}`);

        // Zeige erstes Element
        if (window.totalLoaded === items.length && items.length > 0) {
            showUltraFastItem(0);
        }

        // Verstecke Loading-Indikator
        const loadingIndicator = document.querySelector('.loading-indicator');
        if (loadingIndicator) {
            loadingIndicator.style.display = 'none';
        }

    } catch (error) {
        console.error('❌ Error loading media:', error);
    } finally {
        window.isLoading = false;
    }
}

// Ultra-Fast Media-Element-Erstellung
function createUltraFastMediaElement(item, index) {
    return new Promise((resolve) => {
        console.log(`⚡ Creating ultra-fast element ${index}`);

        const element = document.createElement('div');
        element.className = 'swipe-item';
        element.dataset.index = index;
        element.dataset.mediaId = `${item.creator_id}_${item.filename || item.path.split('/').pop()}`;
        element.dataset.creatorId = item.creator_id;
        element.dataset.creatorName = item.creator_name;
        element.dataset.mediaType = item.type;
        element.dataset.mediaPath = item.path;
        element.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
            display: none;
            align-items: center;
            justify-content: center;
            background: #000;
            opacity: 1;
            transform: translateY(0);
        `;

        // Speichere Metadaten für spätere Verwendung
        element._mediaData = item;

        // Media-Container
        const mediaContainer = document.createElement('div');
        mediaContainer.style.cssText = `
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        `;

        // Minimaler Placeholder
        const placeholder = document.createElement('div');
        placeholder.style.cssText = `
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 24px;
            z-index: 10;
        `;
        placeholder.textContent = '⚡';

        // Media-Element
        const mediaEl = document.createElement(item.type === 'video' ? 'video' : 'img');
        mediaEl.style.cssText = `
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        `;

        if (item.type === 'video') {
            const videoEl = mediaEl; // Cast zu Video-Element
            videoEl.controls = false; // TikTok-style: keine sichtbaren Controls
            videoEl.muted = true;
            videoEl.autoplay = true;
            videoEl.loop = true;
            videoEl.preload = 'metadata';
            videoEl.playsInline = true; // Wichtig für mobile Browser

            // Click-to-pause/play Funktionalität
            videoEl.addEventListener('click', () => {
                if (videoEl.paused) {
                    videoEl.play().catch(e => console.log('Video play failed:', e));
                } else {
                    videoEl.pause();
                }
            });
        }

        // Bestimme beste URL (Thumbnail zuerst für Geschwindigkeit)
        // Prüfe ob der Pfad bereits mit /media/ beginnt
        const mediaPath = item.path.startsWith('/media/') ? item.path : `/media/${item.path}`;
        const thumbnailPath = item.thumbnail_path ?
            (item.thumbnail_path.startsWith('/media/') ? item.thumbnail_path : `/media/${item.thumbnail_path}`) :
            null;

        let imageUrl = thumbnailPath || mediaPath;

        // Sofortiges Laden ohne Verzögerung
        if (item.type === 'video') {
            mediaEl.src = mediaPath;
            mediaEl.autoplay = true;
            mediaEl.loop = true;
            mediaEl.muted = true;
            mediaEl.onloadedmetadata = () => {
                placeholder.style.display = 'none';
            };
        } else {
            mediaEl.src = imageUrl;
            mediaEl.onload = () => {
                placeholder.style.display = 'none';

                // Lade Vollauflösung im Hintergrund (nur wenn Thumbnail)
                if (thumbnailPath && imageUrl.includes('thumbnail')) {
                    setTimeout(() => {
                        const fullImg = new Image();
                        fullImg.onload = () => mediaEl.src = mediaPath;
                        fullImg.src = mediaPath;
                    }, 100);
                }
            };
        }

        // Minimale Creator-Info
        const info = document.createElement('div');
        info.style.cssText = `
            position: absolute;
            bottom: 20px;
            left: 20px;
            color: white;
            background: rgba(0,0,0,0.7);
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 14px;
            z-index: 20;
        `;
        info.textContent = `${item.creator_name} • ${index + 1}/${window.totalItems || '?'}`;

        mediaContainer.appendChild(placeholder);
        mediaContainer.appendChild(mediaEl);
        mediaContainer.appendChild(info);
        element.appendChild(mediaContainer);

        resolve(element);
    });
}

// Ultra-Fast Item-Anzeige mit flüssigen TikTok-style Animationen
function showUltraFastItem(index, direction = 'next') {
    console.log(`👁️ Showing item ${index} (${direction})`);

    const items = document.querySelectorAll('.swipe-item');
    const currentItem = items[window.currentIndex];
    const nextItem = items[index];

    if (!nextItem) return;

    // Animationsrichtung bestimmen
    const isNext = direction === 'next' || index > window.currentIndex;

    // Aktuelles Item ausblenden
    if (currentItem && currentItem !== nextItem) {
        currentItem.style.transform = isNext ? 'translateY(-100%)' : 'translateY(100%)';
        currentItem.style.opacity = '0';

        setTimeout(() => {
            currentItem.style.display = 'none';
            currentItem.style.transform = 'translateY(0)';
            currentItem.style.opacity = '1';
        }, 300);
    }

    // Nächstes Item vorbereiten und einblenden
    nextItem.style.display = 'flex';
    nextItem.style.transform = isNext ? 'translateY(100%)' : 'translateY(-100%)';
    nextItem.style.opacity = '0';

    // Force reflow für smooth animation
    nextItem.offsetHeight;

    // Einblenden mit Animation
    requestAnimationFrame(() => {
        nextItem.style.transform = 'translateY(0)';
        nextItem.style.opacity = '1';
    });

    window.currentIndex = index;

    // Update Interface mit aktuellen Medien-Daten
    updateInterfaceForCurrentMedia(nextItem);

    // Auto-play Videos wenn sie sichtbar werden
    const videoEl = nextItem.querySelector('video');
    if (videoEl) {
        videoEl.currentTime = 0; // Starte vom Anfang
        videoEl.play().catch(e => console.log('Auto-play failed:', e));
    }

    // Pausiere vorherige Videos
    if (currentItem && currentItem !== nextItem) {
        const prevVideoEl = currentItem.querySelector('video');
        if (prevVideoEl) {
            prevVideoEl.pause();
        }
    }

    // KOMPLETT DEAKTIVIERT: Kein automatisches Preloading mehr
    // Das API-Problem mit doppelten Bildern macht automatisches Laden unmöglich
    const remaining = items.length - index;
    console.log(`📊 Items: ${items.length}, Current: ${index}, Remaining: ${remaining}`);

    // Zeige "Mehr laden" Button nur wenn am letzten Bild und noch mehr verfügbar
    showLoadMoreButtonIfNeeded(remaining, index, items.length);
}

// Optimierte Touch-Events mit visueller Rückmeldung
function initUltraFastSwipe() {
    const container = document.querySelector('.swipe-container');
    if (!container) return;

    let startY = 0;
    let startTime = 0;
    let isScrolling = false;
    let currentTouchY = 0;
    let isDragging = false;

    container.addEventListener('touchstart', (e) => {
        startY = e.touches[0].clientY;
        currentTouchY = startY;
        startTime = Date.now();
        isScrolling = false;
        isDragging = false;
    }, { passive: true });

    container.addEventListener('touchmove', (e) => {
        currentTouchY = e.touches[0].clientY;
        const deltaY = currentTouchY - startY;
        const absDeltaY = Math.abs(deltaY);

        if (absDeltaY > 10) {
            isScrolling = true;
            isDragging = true;
            // Verhindere Standard-Scroll-Verhalten nur wenn möglich
            if (e.cancelable) {
                e.preventDefault();
            }

            // Visuelle Rückmeldung während des Swipens
            const items = document.querySelectorAll('.swipe-item');
            const currentItem = items[window.currentIndex];

            if (currentItem) {
                // Begrenze die Bewegung für besseres Gefühl
                const maxMove = 100;
                const moveY = Math.max(-maxMove, Math.min(maxMove, deltaY * 0.3));
                const opacity = Math.max(0.7, 1 - (absDeltaY * 0.002));

                currentItem.style.transform = `translateY(${moveY}px)`;
                currentItem.style.opacity = opacity;
                currentItem.style.transition = 'none';
            }
        }
    }, { passive: false });

    container.addEventListener('touchend', (e) => {
        if (!isScrolling) return;

        const endY = e.changedTouches[0].clientY;
        const deltaY = endY - startY;
        const deltaTime = Date.now() - startTime;
        const velocity = Math.abs(deltaY) / deltaTime;

        // Reset current item styling
        const items = document.querySelectorAll('.swipe-item');
        const currentItem = items[window.currentIndex];

        if (currentItem) {
            currentItem.style.transition = 'transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94), opacity 0.3s ease';
            currentItem.style.transform = 'translateY(0)';
            currentItem.style.opacity = '1';
        }

        // Verbesserte Swipe-Erkennung mit Geschwindigkeit
        const minDistance = 50;
        const minVelocity = 0.3;

        if ((Math.abs(deltaY) > minDistance || velocity > minVelocity) && deltaTime < 800) {
            if (deltaY > 0 && window.currentIndex > 0) {
                // Swipe nach unten - vorheriges Bild
                setTimeout(() => showUltraFastItem(window.currentIndex - 1, 'prev'), 100);
            } else if (deltaY < 0 && window.currentIndex < items.length - 1) {
                // Swipe nach oben - nächstes Bild
                setTimeout(() => showUltraFastItem(window.currentIndex + 1, 'next'), 100);
            }
        }

        isDragging = false;
    }, { passive: true });
}

// Globale Funktionen - DEAKTIVIERT um Konflikte zu vermeiden
// window.loadMoreMedia = loadMoreMediaUltraFast;
window.showItem = showUltraFastItem;
window.createMediaElement = createUltraFastMediaElement;

// Verhindere andere Scripts das Laden zu übernehmen
window.ULTRAFAST_MODE = true;

// Globaler Error-Handler für classList-Fehler
function safeClassListOperation(element, operation, className) {
    if (!element) {
        console.warn(`Element is null for classList.${operation}('${className}')`);
        return false;
    }

    try {
        switch(operation) {
            case 'add':
                element.classList.add(className);
                break;
            case 'remove':
                element.classList.remove(className);
                break;
            case 'toggle':
                element.classList.toggle(className);
                break;
            case 'contains':
                return element.classList.contains(className);
        }
        return true;
    } catch (error) {
        console.error(`Error in classList.${operation}('${className}'):`, error);
        return false;
    }
}

// Ultra-Fast Initialisierung
document.addEventListener('DOMContentLoaded', async () => {
    console.log('🚀 Ultra-Fast Mobile Gallery initialized');

    // Debug: Prüfe DOM-Elemente
    console.log('🔍 Checking DOM elements...');
    const navMenuBtn = document.getElementById('navMenuBtn');
    const bookmarkBtn = document.getElementById('bookmarkBtn');
    console.log('Nav Menu Button:', navMenuBtn);
    console.log('Bookmark Button:', bookmarkBtn);

    // Lade gespeicherten Zustand (async)
    await loadSavedState();

    // Initialisiere Swipe-Events
    initUltraFastSwipe();

    // Initialisiere Interface-Events
    initInterfaceEvents();

    // Lade 10 Bilder initial, Button erscheint nach 5 Bildern
    loadMoreMediaUltraFast(10);

    // DEAKTIVIERT: Kontinuierliches Preloading um Speicher-Overflow zu verhindern
    // Das Preloading wird nur noch manuell beim Swipen ausgelöst
});

function initInterfaceEvents() {
    console.log('🎮 Initializing interface events...');

    // Like Button
    const likeBtn = document.getElementById('likeBtn');
    if (likeBtn) {
        likeBtn.addEventListener('click', toggleLike);
        console.log('✅ Like button event listener added');
    } else {
        console.log('❌ Like button not found');
    }

    // Bookmark Button
    const bookmarkBtn = document.getElementById('bookmarkBtn');
    if (bookmarkBtn) {
        bookmarkBtn.addEventListener('click', toggleBookmark);
        console.log('✅ Bookmark button event listener added');
    } else {
        console.log('❌ Bookmark button not found');
    }

    // Info Button
    const infoBtn = document.getElementById('infoBtn');
    if (infoBtn) {
        infoBtn.addEventListener('click', toggleInfo);
        console.log('✅ Info button event listener added');
    } else {
        console.log('❌ Info button not found');
    }

    // Creator Filter Button
    const creatorFilterBtn = document.getElementById('creatorFilterBtn');
    if (creatorFilterBtn) {
        creatorFilterBtn.addEventListener('click', toggleCreatorFilter);
        console.log('✅ Creator filter button event listener added');
    } else {
        console.log('❌ Creator filter button not found');
    }

    // Navigation Menu
    const navMenuBtn = document.getElementById('navMenuBtn');
    if (navMenuBtn) {
        navMenuBtn.addEventListener('click', toggleNavMenu);
        console.log('✅ Navigation menu button event listener added');
    } else {
        console.log('❌ Navigation menu button not found');
    }

    // Navigation Items (initial setup)
    initMainNavigation();

    // Media Filter Buttons (neue Bottom-Filter)
    document.querySelectorAll('.media-filter-btn').forEach(btn => {
        btn.addEventListener('click', () => {
            setMediaTypeFilter(btn.dataset.type);
        });
    });

    // Bookmark Modal Events
    const bookmarkModalCancel = document.getElementById('bookmarkModalCancel');
    if (bookmarkModalCancel) {
        bookmarkModalCancel.addEventListener('click', hideBookmarkModal);
    }

    const bookmarkModalConfirm = document.getElementById('bookmarkModalConfirm');
    if (bookmarkModalConfirm) {
        bookmarkModalConfirm.addEventListener('click', createNewListFromModal);
    }

    const newListInput = document.getElementById('newListInput');
    if (newListInput) {
        newListInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                createNewListFromModal();
            }
        });
    }

    // Info Overlay schließen bei Klick außerhalb
    const infoOverlay = document.getElementById('infoOverlay');
    if (infoOverlay) {
        infoOverlay.addEventListener('click', (e) => {
            if (e.target === infoOverlay) {
                toggleInfo();
            }
        });
    }

    // Bookmark Modal schließen bei Klick außerhalb
    const bookmarkModal = document.getElementById('bookmarkModal');
    if (bookmarkModal) {
        bookmarkModal.addEventListener('click', (e) => {
            if (e.target === bookmarkModal) {
                hideBookmarkModal();
            }
        });
    }

    // Klick außerhalb schließt Navigation
    document.addEventListener('click', (e) => {
        const navMenu = document.querySelector('.nav-menu');
        if (navMenu && !navMenu.contains(e.target) && window.interfaceState.navMenuVisible) {
            hideNavMenu();
        }
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', (e) => {
        switch(e.key) {
            case 'l':
            case 'L':
                toggleLike();
                break;
            case 'b':
            case 'B':
                toggleBookmark();
                break;
            case 'i':
            case 'I':
                toggleInfo();
                break;
            case 'c':
            case 'C':
                toggleCreatorFilter();
                break;
            case 'm':
            case 'M':
                toggleNavMenu();
                break;
            case 'Escape':
                if (window.interfaceState.bookmarkModalVisible) {
                    hideBookmarkModal();
                } else if (window.interfaceState.infoVisible) {
                    toggleInfo();
                } else if (window.interfaceState.navMenuVisible) {
                    hideNavMenu();
                }
                break;
            case '1':
                setMediaTypeFilter('all');
                break;
            case '2':
                setMediaTypeFilter('image');
                break;
            case '3':
                setMediaTypeFilter('video');
                break;
        }
    });

    console.log('🎮 Interface events initialized');
}

// Interface-Funktionen
function updateInterfaceForCurrentMedia(mediaElement) {
    if (!mediaElement || !mediaElement._mediaData) return;

    const mediaData = mediaElement._mediaData;
    window.interfaceState.currentMediaData = mediaData;

    // Update Creator Info
    const creatorName = document.getElementById('creatorName');
    if (creatorName) {
        creatorName.textContent = window.interfaceState.creatorFilter ?
            `🎯 ${mediaData.creator_name}` :
            mediaData.creator_name;
    }

    // Update Button States
    updateButtonStates(mediaElement.dataset.mediaId);
}

function updateButtonStates(mediaId) {
    // Like Button
    const likeBtn = document.getElementById('likeBtn');
    if (likeBtn) {
        if (window.interfaceState.likedItems.has(mediaId)) {
            safeClassListOperation(likeBtn, 'add', 'liked');
        } else {
            safeClassListOperation(likeBtn, 'remove', 'liked');
        }
    }

    // Bookmark Button - prüfe ob in irgendeiner Liste
    const bookmarkBtn = document.getElementById('bookmarkBtn');
    if (bookmarkBtn) {
        let isBookmarked = false;
        for (const listData of Object.values(window.interfaceState.bookmarkLists)) {
            if (listData.items.has(mediaId)) {
                isBookmarked = true;
                break;
            }
        }

        if (isBookmarked) {
            safeClassListOperation(bookmarkBtn, 'add', 'active');
        } else {
            safeClassListOperation(bookmarkBtn, 'remove', 'active');
        }
    }

    // Creator Filter Button
    const creatorFilterBtn = document.getElementById('creatorFilterBtn');
    if (creatorFilterBtn) {
        if (window.interfaceState.creatorFilter) {
            safeClassListOperation(creatorFilterBtn, 'add', 'active');
        } else {
            safeClassListOperation(creatorFilterBtn, 'remove', 'active');
        }
    }
}

async function toggleLike() {
    const currentItem = document.querySelectorAll('.swipe-item')[window.currentIndex];
    if (!currentItem) return;

    const mediaId = currentItem.dataset.mediaId;
    const likeBtn = document.getElementById('likeBtn');

    if (!likeBtn) {
        console.warn('Like button not found');
        return;
    }

    const wasLiked = window.interfaceState.likedItems.has(mediaId);
    let serverSuccess = false;

    if (wasLiked) {
        // Entferne Like
        window.interfaceState.likedItems.delete(mediaId);
        safeClassListOperation(likeBtn, 'remove', 'liked');
        console.log('❤️ Unliked:', mediaId);

        // Versuche auf Server zu speichern
        if (typeof removeLikeFromServer === 'function') {
            serverSuccess = await removeLikeFromServer(mediaId);
        }
    } else {
        // Füge Like hinzu
        window.interfaceState.likedItems.add(mediaId);
        safeClassListOperation(likeBtn, 'add', 'liked');
        console.log('❤️ Liked:', mediaId);

        // Versuche auf Server zu speichern
        if (typeof addLikeToServer === 'function') {
            serverSuccess = await addLikeToServer(mediaId);
        }
    }

    // Fallback zu localStorage wenn Server nicht erreichbar
    if (!serverSuccess) {
        console.log('⚠️ Server nicht erreichbar, speichere in localStorage');
        localStorage.setItem('likedItems', JSON.stringify([...window.interfaceState.likedItems]));
    }
}

function toggleBookmark() {
    const currentItem = document.querySelectorAll('.swipe-item')[window.currentIndex];
    if (!currentItem) return;

    // Zeige Bookmark-Listen-Modal
    showBookmarkModal();
}

function toggleInfo() {
    const infoOverlay = document.getElementById('infoOverlay');
    const infoContent = document.getElementById('infoContent');
    const currentItem = document.querySelectorAll('.swipe-item')[window.currentIndex];

    if (!infoOverlay || !infoContent || !currentItem) {
        console.warn('Info elements not found:', {
            infoOverlay: !!infoOverlay,
            infoContent: !!infoContent,
            currentItem: !!currentItem
        });
        return;
    }

    if (window.interfaceState.infoVisible) {
        safeClassListOperation(infoOverlay, 'remove', 'visible');
        window.interfaceState.infoVisible = false;
    } else {
        // Lade Info-Daten
        const mediaData = currentItem._mediaData;
        if (mediaData) {
            infoContent.innerHTML = `
                <div class="info-item">
                    <span class="info-label">Creator:</span>
                    <span class="info-value">${mediaData.creator_name}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Typ:</span>
                    <span class="info-value">${mediaData.type === 'video' ? '📹 Video' : '📷 Bild'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Pfad:</span>
                    <span class="info-value">${mediaData.path}</span>
                </div>
                ${mediaData.metadata && Object.keys(mediaData.metadata).length > 0 ?
                    Object.entries(mediaData.metadata).map(([key, value]) =>
                        `<div class="info-item">
                            <span class="info-label">${key}:</span>
                            <span class="info-value">${value}</span>
                        </div>`
                    ).join('') :
                    '<div class="info-item"><span class="info-value">Keine zusätzlichen Metadaten verfügbar</span></div>'
                }
            `;
        }

        safeClassListOperation(infoOverlay, 'add', 'visible');
        window.interfaceState.infoVisible = true;
    }
}

function toggleCreatorFilter() {
    const currentItem = document.querySelectorAll('.swipe-item')[window.currentIndex];
    if (!currentItem) return;

    const creatorId = currentItem.dataset.creatorId;
    const creatorFilterBtn = document.getElementById('creatorFilterBtn');

    if (window.interfaceState.creatorFilter === creatorId) {
        // Filter entfernen
        window.interfaceState.creatorFilter = null;
        if (creatorFilterBtn) {
            safeClassListOperation(creatorFilterBtn, 'remove', 'active');
        }
        console.log('🎯 Creator filter removed');
    } else {
        // Filter setzen
        window.interfaceState.creatorFilter = creatorId;
        if (creatorFilterBtn) {
            safeClassListOperation(creatorFilterBtn, 'add', 'active');
        }
        console.log('🎯 Creator filter set to:', creatorId);
    }

    // Galerie neu laden (nur für normale Ansicht)
    if (window.interfaceState.currentView === 'all') {
        reloadGalleryWithFilters();
    }
}

function setMediaTypeFilter(type) {
    window.interfaceState.mediaTypeFilter = type;

    // Update Button States (neue Bottom-Filter)
    const mediaFilterBtns = document.querySelectorAll('.media-filter-btn');
    if (mediaFilterBtns.length > 0) {
        mediaFilterBtns.forEach(btn => {
            if (btn) {
                safeClassListOperation(btn, 'remove', 'active');
                if (btn.dataset.type === type) {
                    safeClassListOperation(btn, 'add', 'active');
                }
            }
        });
    }

    console.log('📷📹 Media type filter set to:', type);

    // Galerie neu laden (nur für normale Ansicht)
    if (window.interfaceState.currentView === 'all') {
        reloadGalleryWithFilters();
    }
}

function reloadGalleryWithFilters() {
    console.log('🔄 Reloading gallery with filters...');

    // Verhindere mehrfache gleichzeitige Reloads
    if (window.isReloading) {
        console.log('⏳ Already reloading...');
        return;
    }

    window.isReloading = true;

    // Reset state komplett
    window.totalLoaded = 0;
    window.totalItems = 0;
    window.currentIndex = 0;
    window.isLoading = false;
    window.lastLoadedOffset = -1; // Reset Offset-Tracking

    // Clear container
    let container = document.getElementById('swipeContainer');
    if (!container) {
        container = document.querySelector('.swipe-container');
    }
    if (container) {
        container.innerHTML = '';
    } else {
        console.error('❌ Container not found for reloading gallery');
    }

    // Reload with new filters
    setTimeout(() => {
        loadMoreMediaUltraFast(10);
        window.isReloading = false;
    }, 200); // Längere Verzögerung für sauberen Reset
}

async function loadSavedState() {
    // Lade gespeicherte Likes und Bookmark-Listen vom Server
    console.log('🔄 Lade gespeicherten Zustand vom Server...');

    try {
        // Versuche Server-Storage zu initialisieren
        const serverSuccess = await initServerStorage();

        if (!serverSuccess) {
            console.log('⚠️ Server nicht erreichbar, verwende localStorage als Fallback');
            fallbackToLocalStorage();
        }
    } catch (e) {
        console.warn('Fehler beim Laden vom Server, verwende localStorage:', e);
        fallbackToLocalStorage();
    }
}

// Bookmark-Listen-System (Server-basiert)
async function createBookmarkList(name) {
    console.log('📝 Erstelle Bookmark-Liste:', name);

    // Versuche auf Server zu erstellen
    let listId = null;
    if (typeof createBookmarkListOnServer === 'function') {
        listId = await createBookmarkListOnServer(name);
    }

    if (listId) {
        // Erfolgreich auf Server erstellt
        window.interfaceState.bookmarkLists[listId] = {
            name: name,
            items: new Set()
        };
        console.log('✅ Bookmark-Liste auf Server erstellt:', listId);
        return listId;
    } else {
        // Fallback zu lokaler Erstellung
        console.log('⚠️ Server nicht erreichbar, erstelle lokal');
        const localListId = 'list_' + Date.now();
        window.interfaceState.bookmarkLists[localListId] = {
            name: name,
            items: new Set()
        };
        saveBookmarkListsToLocalStorage();
        return localListId;
    }
}

function saveBookmarkListsToLocalStorage() {
    const listsToSave = {};
    for (const [listId, listData] of Object.entries(window.interfaceState.bookmarkLists)) {
        listsToSave[listId] = {
            name: listData.name,
            items: [...listData.items]
        };
    }
    localStorage.setItem('bookmarkLists', JSON.stringify(listsToSave));
}

async function addToBookmarkList(listId, mediaId) {
    if (!window.interfaceState.bookmarkLists[listId]) {
        console.error('Bookmark-Liste nicht gefunden:', listId);
        return false;
    }

    // Lokale Änderung
    window.interfaceState.bookmarkLists[listId].items.add(mediaId);

    // Versuche auf Server zu speichern
    let serverSuccess = false;
    if (typeof addToBookmarkListOnServer === 'function') {
        serverSuccess = await addToBookmarkListOnServer(listId, mediaId);
    }

    if (!serverSuccess) {
        console.log('⚠️ Server nicht erreichbar, speichere in localStorage');
        saveBookmarkListsToLocalStorage();
    }

    console.log(`🔖 Added to list ${listId}:`, mediaId);
    return true;
}

async function removeFromBookmarkList(listId, mediaId) {
    if (!window.interfaceState.bookmarkLists[listId]) {
        console.error('Bookmark-Liste nicht gefunden:', listId);
        return false;
    }

    // Lokale Änderung
    window.interfaceState.bookmarkLists[listId].items.delete(mediaId);

    // Versuche auf Server zu speichern
    let serverSuccess = false;
    if (typeof removeFromBookmarkListOnServer === 'function') {
        serverSuccess = await removeFromBookmarkListOnServer(listId, mediaId);
    }

    if (!serverSuccess) {
        console.log('⚠️ Server nicht erreichbar, speichere in localStorage');
        saveBookmarkListsToLocalStorage();
    }

    console.log(`🔖 Removed from list ${listId}:`, mediaId);
    return true;
}

function isInBookmarkList(listId, mediaId) {
    return window.interfaceState.bookmarkLists[listId]?.items.has(mediaId) || false;
}

function showBookmarkModal() {
    const modal = document.getElementById('bookmarkModal');
    const container = document.getElementById('bookmarkListsContainer');

    if (!modal || !container) {
        console.warn('Bookmark modal elements not found:', {
            modal: !!modal,
            container: !!container
        });
        return;
    }

    // Lade aktuelle Listen
    container.innerHTML = '';

    for (const [listId, listData] of Object.entries(window.interfaceState.bookmarkLists)) {
        const listItem = document.createElement('div');
        listItem.className = 'bookmark-list-item';
        listItem.dataset.listId = listId;

        const currentItem = document.querySelectorAll('.swipe-item')[window.currentIndex];
        const mediaId = currentItem?.dataset.mediaId;
        const isInList = mediaId && isInBookmarkList(listId, mediaId);

        listItem.innerHTML = `
            <div class="bookmark-list-name">${isInList ? '✓ ' : ''}${listData.name}</div>
            <div class="bookmark-list-count">${listData.items.size}</div>
        `;

        listItem.addEventListener('click', async () => {
            if (mediaId) {
                console.log('📝 Bookmark list clicked:', listId, mediaId);
                try {
                    if (isInList) {
                        await removeFromBookmarkList(listId, mediaId);
                    } else {
                        await addToBookmarkList(listId, mediaId);
                    }
                    hideBookmarkModal();
                    updateButtonStates(mediaId);
                } catch (error) {
                    console.error('❌ Error handling bookmark:', error);
                    hideBookmarkModal();
                }
            }
        });

        container.appendChild(listItem);
    }

    safeClassListOperation(modal, 'add', 'visible');
    window.interfaceState.bookmarkModalVisible = true;
}

function hideBookmarkModal() {
    const modal = document.getElementById('bookmarkModal');
    const newListInput = document.getElementById('newListInput');
    const confirmBtn = document.getElementById('bookmarkModalConfirm');

    if (modal) {
        safeClassListOperation(modal, 'remove', 'visible');
    }
    if (newListInput) {
        newListInput.style.display = 'none';
        newListInput.value = '';
    }
    if (confirmBtn) {
        confirmBtn.style.display = 'none';
    }
    window.interfaceState.bookmarkModalVisible = false;
}

function showCreateListInput() {
    const newListInput = document.getElementById('newListInput');
    const confirmBtn = document.getElementById('bookmarkModalConfirm');

    if (newListInput && confirmBtn) {
        newListInput.style.display = 'block';
        confirmBtn.style.display = 'block';
        newListInput.focus();
    } else {
        console.warn('Create list input elements not found:', {
            newListInput: !!newListInput,
            confirmBtn: !!confirmBtn
        });
    }
}

async function createNewListFromModal() {
    const newListInput = document.getElementById('newListInput');

    if (!newListInput) {
        console.warn('New list input not found');
        return;
    }

    const listName = newListInput.value.trim();

    if (listName) {
        try {
            const listId = await createBookmarkList(listName);

            if (listId) {
                // Füge aktuelles Medium zur neuen Liste hinzu
                const currentItem = document.querySelectorAll('.swipe-item')[window.currentIndex];
                const mediaId = currentItem?.dataset.mediaId;
                if (mediaId) {
                    await addToBookmarkList(listId, mediaId);
                    updateButtonStates(mediaId);
                }
            }
        } catch (error) {
            console.error('❌ Error creating new list:', error);
        }

        hideBookmarkModal();
    }
}

// Navigation und Views
function toggleNavMenu() {
    const dropdown = document.getElementById('navDropdown');
    if (!dropdown) return;

    if (window.interfaceState.navMenuVisible) {
        safeClassListOperation(dropdown, 'remove', 'visible');
        window.interfaceState.navMenuVisible = false;
    } else {
        safeClassListOperation(dropdown, 'add', 'visible');
        window.interfaceState.navMenuVisible = true;
    }
}

function hideNavMenu() {
    const dropdown = document.getElementById('navDropdown');
    if (dropdown) {
        safeClassListOperation(dropdown, 'remove', 'visible');
    }
    window.interfaceState.navMenuVisible = false;
}

function switchToView(viewType, listId = null) {
    console.log('🔄 Switching to view:', viewType, listId);
    hideNavMenu();

    // Verhindere mehrfache gleichzeitige View-Switches
    if (window.isSwitchingView) {
        console.log('⏳ Already switching view...');
        return;
    }

    const oldView = window.interfaceState.currentView;

    if (viewType === 'all') {
        window.interfaceState.currentView = 'all';
        updateCreatorInfo('Alle Creator');
    } else if (viewType === 'likes') {
        window.interfaceState.currentView = 'likes';
        updateCreatorInfo('❤️ Gelikte Medien');
    } else if (viewType === 'list' && listId) {
        window.interfaceState.currentView = `list:${listId}`;
        const listName = window.interfaceState.bookmarkLists[listId]?.name || 'Unbekannte Liste';
        updateCreatorInfo(`🔖 ${listName}`);
    }

    // Update Navigation
    updateNavigation();

    // Nur neu laden wenn sich die View geändert hat
    if (oldView !== window.interfaceState.currentView) {
        console.log('📱 View changed from', oldView, 'to', window.interfaceState.currentView);

        window.isSwitchingView = true;

        // Reset loading state komplett
        window.isLoading = false;
        window.isReloading = false;
        window.lastLoadedOffset = -1; // Reset Offset-Tracking

        clearCurrentMedia();
        window.totalLoaded = 0;
        window.currentIndex = 0;

        setTimeout(() => {
            if (viewType === 'likes') {
                window.totalItems = window.interfaceState.likedItems.size;
                loadLikedMedia(10);
            } else if (viewType === 'list' && listId) {
                const list = window.interfaceState.bookmarkLists[listId];
                window.totalItems = list ? list.items.size : 0;
                loadBookmarkListMedia(listId, 10);
            } else {
                // Normale Ansicht
                window.totalItems = 0; // Wird von API gesetzt
                loadMoreMediaUltraFast(10);
            }
            window.isSwitchingView = false;
        }, 100);
    } else {
        console.log('📱 View unchanged, no reload needed');
    }
}

function showLoadMoreButtonIfNeeded(remaining, currentIndex, totalItems) {
    // Zeige Button wenn weniger als 5 Bilder übrig und noch mehr verfügbar
    if (remaining <= 5 && window.totalLoaded < window.totalItems && window.interfaceState.currentView === 'all') {
        const existingBtn = document.getElementById('loadMoreBtn');
        if (!existingBtn) {
            // Versuche beide Container-Selektoren
            let container = document.getElementById('swipeContainer');
            if (!container) {
                container = document.querySelector('.swipe-container');
            }

            if (container) {
                const loadMoreBtn = document.createElement('div');
                loadMoreBtn.id = 'loadMoreBtn';
                loadMoreBtn.className = 'load-more-btn';
                loadMoreBtn.innerHTML = '⬇️ Mehr laden (10 Bilder)';
                loadMoreBtn.addEventListener('click', () => {
                    console.log('🔄 Load more button clicked');
                    loadMoreBtn.remove();
                    loadMoreMediaUltraFast(10);
                });
                container.appendChild(loadMoreBtn);
                console.log('📱 "Mehr laden" Button angezeigt (5 Bilder übrig)');
            } else {
                console.error('❌ Container not found for load more button');
            }
        }
    } else {
        // Entferne Button wenn nicht mehr benötigt
        const existingBtn = document.getElementById('loadMoreBtn');
        if (existingBtn) {
            existingBtn.remove();
        }
    }
}

function clearCurrentMedia() {
    console.log('🧹 Clearing current media...');
    let container = document.getElementById('swipeContainer');
    if (!container) {
        container = document.querySelector('.swipe-container');
    }
    if (container) {
        container.innerHTML = '';
    } else {
        console.error('❌ Container not found for clearing media');
    }
}

function updateCreatorInfo(text) {
    const creatorName = document.getElementById('creatorName');
    if (creatorName) {
        creatorName.textContent = text;
    }
}

function updateNavigation() {
    // Update aktive Navigation
    const navItems = document.querySelectorAll('.nav-item');
    if (navItems.length > 0) {
        navItems.forEach(item => {
            if (item) {
                safeClassListOperation(item, 'remove', 'active');
            }
        });
    }

    if (window.interfaceState.currentView === 'all') {
        const navHome = document.getElementById('navHome');
        if (navHome) {
            safeClassListOperation(navHome, 'add', 'active');
        }
    } else if (window.interfaceState.currentView === 'likes') {
        const navLikes = document.getElementById('navLikes');
        if (navLikes) {
            safeClassListOperation(navLikes, 'add', 'active');
        }
    }
}

// Spezielle Loading-Funktionen für Views
async function loadLikedMedia(limit) {
    console.log('❤️ Loading liked media...');

    if (window.isLoading) {
        console.log('⏳ Already loading...');
        return;
    }

    window.isLoading = true;

    if (window.interfaceState.likedItems.size === 0) {
        console.log('No liked items found');
        window.isLoading = false;
        return;
    }

    // Simuliere API-Response für gelikte Medien
    // In einer echten Implementierung würde man die Medien-Details vom Server laden
    const likedArray = [...window.interfaceState.likedItems];
    const batch = likedArray.slice(window.totalLoaded, window.totalLoaded + limit);

    if (batch.length === 0) {
        console.log('No more liked items to load');
        window.isLoading = false;
        return;
    }

    // Erstelle Mock-Medien-Objekte (in echter App würde man Details vom Server laden)
    const mockItems = batch.map((mediaId, index) => {
        const parts = mediaId.split('_');
        const creatorId = parts[0];
        const filename = parts.slice(1).join('_'); // Falls Filename Underscores enthält

        // Prüfe ob filename bereits ein Pfad ist
        const isFullPath = filename.includes('/');
        const actualFilename = isFullPath ? filename.split('/').pop() : filename;
        const path = isFullPath ? filename : `cretors/${creatorId}/${filename}`;

        return {
            path: path,
            type: actualFilename.toLowerCase().includes('.mp4') || actualFilename.toLowerCase().includes('.webm') ? 'video' : 'image',
            creator_id: creatorId,
            creator_name: creatorId,
            filename: actualFilename,
            metadata: {}
        };
    });

    // Verarbeite wie normale API-Response
    const container = document.querySelector('.swipe-container');
    if (!container) {
        console.error('Container not found');
        window.isLoading = false;
        return;
    }

    const promises = mockItems.map((item, index) => {
        return createUltraFastMediaElement(item, window.totalLoaded + index);
    });

    const mediaElements = await Promise.all(promises);

    // Füge alle Elemente gleichzeitig hinzu
    mediaElements.forEach(element => {
        if (element) {
            container.appendChild(element);
        }
    });

    window.totalLoaded += batch.length;
    window.totalItems = window.interfaceState.likedItems.size;

    console.log(`❤️ Loaded ${batch.length} liked items`);

    window.isLoading = false;

    // Zeige erstes Element wenn wir gerade erst geladen haben
    if (window.totalLoaded === batch.length && batch.length > 0) {
        setTimeout(() => {
            showUltraFastItem(0);
        }, 100);
    }
}

async function loadBookmarkListMedia(listId, limit) {
    console.log(`🔖 Loading bookmark list: ${listId}`);

    if (window.isLoading) {
        console.log('⏳ Already loading...');
        return;
    }

    const list = window.interfaceState.bookmarkLists[listId];
    if (!list || list.items.size === 0) {
        console.log('📝 No items in bookmark list or list not found');

        // Zeige leere Nachricht
        const container = document.querySelector('.swipe-container');
        if (container) {
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">🔖</div>
                    <div class="empty-title">Liste ist leer</div>
                    <div class="empty-subtitle">Fügen Sie Medien zu dieser Liste hinzu</div>
                </div>
            `;
        }
        return;
    }

    window.isLoading = true;

    try {
        const itemsArray = [...list.items];
        const batch = itemsArray.slice(window.totalLoaded, window.totalLoaded + limit);

        if (batch.length === 0) {
            console.log('No more bookmark items to load');
            window.isLoading = false;
            return;
        }

        console.log(`🔖 Loading ${batch.length} items from list "${list.name}"`);

        // Erstelle Mock-Medien-Objekte basierend auf gespeicherten IDs
        const mockItems = batch.map((mediaId) => {
            // MediaId kann verschiedene Formate haben
            let path, creatorId, filename;

            if (mediaId.includes('/')) {
                // Vollständiger Pfad gespeichert
                path = mediaId;
                const pathParts = mediaId.split('/');
                filename = pathParts[pathParts.length - 1];
                creatorId = pathParts[pathParts.length - 2] || 'unknown';
            } else {
                // Nur Dateiname gespeichert (Legacy-Format)
                const parts = mediaId.split('_');
                creatorId = parts[0] || 'unknown';
                filename = parts.slice(1).join('_') || mediaId;
                path = `cretors/${creatorId}/${filename}`;
            }

            return {
                path: path,
                type: filename.toLowerCase().includes('.mp4') || filename.toLowerCase().includes('.webm') ? 'video' : 'image',
                creator_id: creatorId,
                creator_name: creatorId,
                filename: filename,
                metadata: {}
            };
        });

        // Verarbeite wie normale API-Response
        const container = document.querySelector('.swipe-container');
        if (!container) {
            console.error('❌ Container not found');
            window.isLoading = false;
            return;
        }

        const promises = mockItems.map((item, index) => {
            return createUltraFastMediaElement(item, window.totalLoaded + index);
        });

        const mediaElements = await Promise.all(promises);

        // Füge alle Elemente gleichzeitig hinzu
        mediaElements.forEach(element => {
            if (element) {
                container.appendChild(element);
            }
        });

        window.totalLoaded += batch.length;
        window.totalItems = list.items.size;

        console.log(`✅ Loaded ${batch.length} bookmark items from list: ${list.name}`);

        // Zeige erstes Element wenn wir gerade erst geladen haben
        if (window.totalLoaded === batch.length && batch.length > 0) {
            setTimeout(() => {
                showUltraFastItem(0);
            }, 100);
        }

    } catch (error) {
        console.error('❌ Error loading bookmark list media:', error);
    } finally {
        window.isLoading = false;
    }
}

function showBookmarkListsNavigation() {
    hideNavMenu();

    // Erstelle dynamisches Dropdown für Bookmark-Listen
    const dropdown = document.getElementById('navDropdown');
    if (!dropdown) {
        console.warn('Navigation dropdown not found');
        return;
    }

    dropdown.innerHTML = `
        <div class="nav-item" id="navBackToMain">
            <span>←</span>
            <span>Zurück</span>
        </div>
        <div class="nav-divider"></div>
    `;

    // Füge alle Bookmark-Listen hinzu
    for (const [listId, listData] of Object.entries(window.interfaceState.bookmarkLists)) {
        const listItem = document.createElement('div');
        listItem.className = 'nav-item';
        listItem.innerHTML = `
            <span>🔖</span>
            <span>${listData.name} (${listData.items.size})</span>
        `;
        listItem.addEventListener('click', () => {
            switchToView('list', listId);
        });
        dropdown.appendChild(listItem);
    }

    // Zurück-Button mit null-Check
    const navBackToMain = document.getElementById('navBackToMain');
    if (navBackToMain) {
        navBackToMain.addEventListener('click', () => {
            initMainNavigation();
            hideNavMenu();
        });
    }

    if (dropdown) {
        safeClassListOperation(dropdown, 'add', 'visible');
    }
    window.interfaceState.navMenuVisible = true;
}

function initMainNavigation() {
    const dropdown = document.getElementById('navDropdown');
    if (!dropdown) {
        console.warn('Navigation dropdown not found');
        return;
    }

    dropdown.innerHTML = `
        <div class="nav-item" id="navHome">
            <span>🏠</span>
            <span>Alle Medien</span>
        </div>
        <div class="nav-divider"></div>
        <div class="nav-item" id="navLikes">
            <span>❤️</span>
            <span>Gelikte Medien</span>
        </div>
        <div class="nav-item" id="navBookmarks">
            <span>🔖</span>
            <span>Bookmark Listen</span>
        </div>
        <div class="nav-divider"></div>
        <div class="nav-item" id="navCreateList">
            <span>➕</span>
            <span>Neue Liste erstellen</span>
        </div>
    `;

    // Event Listener neu hinzufügen mit null-Checks
    const navHome = document.getElementById('navHome');
    const navLikes = document.getElementById('navLikes');
    const navBookmarks = document.getElementById('navBookmarks');
    const navCreateList = document.getElementById('navCreateList');

    if (navHome) {
        navHome.addEventListener('click', () => switchToView('all'));
    }
    if (navLikes) {
        navLikes.addEventListener('click', () => switchToView('likes'));
    }
    if (navBookmarks) {
        navBookmarks.addEventListener('click', showBookmarkListsNavigation);
    }
    if (navCreateList) {
        navCreateList.addEventListener('click', () => {
            hideNavMenu();
            showCreateListInput();
            showBookmarkModal();
        });
    }

    updateNavigation();
}

console.log('⚡ Ultra-Fast Mobile Gallery module loaded');
