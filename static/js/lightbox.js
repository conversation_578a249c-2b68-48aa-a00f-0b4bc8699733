/**
 * Lightbox implementation for CiitAI-Galerie
 * Handles display of images and videos in a modal lightbox
 */

document.addEventListener('DOMContentLoaded', function() {
    // Lightbox elements
    const lightbox = document.getElementById('mediaLightbox');
    if (!lightbox) return; // Exit if lightbox not found

    const lightboxContent = lightbox.querySelector('.lightbox-content');
    const lightboxClose = lightbox.querySelector('.lightbox-close');
    const prevButton = document.getElementById('prevButton');
    const nextButton = document.getElementById('nextButton');
    const slideshowButton = document.getElementById('slideshowButton');
    const lightboxCounter = lightbox.querySelector('.lightbox-counter');

    // State variables
    let currentIndex = 0;
    let mediaArray = [];
    let slideshowInterval = null;
    let isSlideshow = false;
    let interfaceHidden = false;
    let lastClickTime = 0;

    // Interface state (sync with mobile version)
    window.interfaceState = window.interfaceState || {
        likedItems: new Set(),
        bookmarkLists: {},
        currentView: 'all',
        currentMediaData: null,
        bookmarkModalVisible: false
    };

    // Collect all media items from the current active tab
    function collectMediaItems() {
        const activeTab = document.querySelector('.tab-pane.active');
        if (!activeTab) return [];
        return Array.from(activeTab.querySelectorAll('.media-item'));
    }

    // Show media at the specified index
    function showMedia(index) {
        if (mediaArray.length === 0) return;

        // Ensure index is within bounds
        currentIndex = (index + mediaArray.length) % mediaArray.length;
        const item = mediaArray[currentIndex];
        const type = item.dataset.type;
        const path = item.dataset.path;

        if (!path) {
            console.error("Media path is undefined");
            showError("Fehler: Medienpfad nicht gefunden");
            return;
        }

        // Update current media data
        window.interfaceState.currentMediaData = {
            path: path,
            type: type,
            creator_name: getCurrentCreatorName(),
            filename: path.split('/').pop()
        };

        // Update counter
        updateCounter();

        // Update button states
        updateButtonStates();

        // Show loading indicator
        showLoading();

        if (type === 'image') {
            loadImage(path);
        } else if (type === 'video') {
            loadVideo(path);
            stopSlideshow(); // Always stop slideshow when showing a video
        }
    }

    // Update the counter display
    function updateCounter() {
        if (lightboxCounter) {
            lightboxCounter.textContent = `${currentIndex + 1} / ${mediaArray.length}`;
        }
    }

    // Show loading indicator
    function showLoading() {
        lightboxContent.innerHTML = `
            <div class="d-flex justify-content-center align-items-center" style="height: 100%;">
                <div class="spinner-border text-light" role="status">
                    <span class="visually-hidden">Lädt...</span>
                </div>
            </div>`;
    }

    // Show error message
    function showError(message) {
        lightboxContent.innerHTML = `
            <div class="d-flex flex-column justify-content-center align-items-center text-light h-100 p-4 text-center">
                <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                <p class="mb-3">${message}</p>
                <button class="btn btn-sm btn-outline-light" onclick="location.reload()">
                    <i class="fas fa-sync-alt me-1"></i> Erneut versuchen
                </button>
            </div>`;
    }

    // Load and display an image
    function loadImage(path) {
        const img = new Image();

        img.onload = function() {
            lightboxContent.innerHTML = `
                <img src="${path}"
                     alt="Vollbild"
                     class="lightbox-media"
                     loading="eager"
                     style="max-width: 100%; max-height: 90vh; object-fit: contain;">`;

            // Add fade-in effect
            const media = lightboxContent.querySelector('.lightbox-media');
            if (media) {
                media.style.opacity = '0';
                setTimeout(() => {
                    media.style.transition = 'opacity 0.3s ease';
                    media.style.opacity = '1';
                }, 10);
            }
        };

        img.onerror = function() {
            showError("Fehler beim Laden des Bildes");
        };

        img.src = path;
    }

    // Load and display a video
    function loadVideo(path) {
        const video = document.createElement('video');
        video.controls = true;
        video.autoplay = true;
        video.className = 'lightbox-media';
        video.style.maxWidth = '100%';
        video.style.maxHeight = '90vh';

        const source = document.createElement('source');
        source.src = path;
        source.type = 'video/mp4';

        video.appendChild(source);
        video.onerror = function() {
            showError("Fehler beim Laden des Videos");
        };

        lightboxContent.innerHTML = '';
        lightboxContent.appendChild(video);

        // Pause slideshow when video starts playing
        video.addEventListener('play', stopSlideshow);
    }

    // Start slideshow
    function startSlideshow() {
        if (isSlideshow || mediaArray.length === 0) return;

        isSlideshow = true;
        updateSlideshowButton();

        // Start with current media if it's an image, otherwise find next image
        let startIndex = currentIndex;
        if (mediaArray[startIndex].dataset.type !== 'image') {
            startIndex = findNextImageIndex(startIndex);
            if (startIndex === -1) {
                stopSlideshow();
                return;
            }
            showMedia(startIndex);
        }

        // Start slideshow interval
        slideshowInterval = setInterval(() => {
            const nextIndex = findNextImageIndex(currentIndex + 1);
            if (nextIndex !== -1) {
                showMedia(nextIndex);
            } else {
                stopSlideshow();
            }
        }, 5000); // 5 seconds per slide

        // Add visual feedback
        document.body.classList.add('slideshow-active');
    }

    // Find next image index (wraps around)
    function findNextImageIndex(startIndex) {
        for (let i = 0; i < mediaArray.length; i++) {
            const idx = (startIndex + i) % mediaArray.length;
            if (mediaArray[idx].dataset.type === 'image') {
                return idx;
            }
        }
        return -1;
    }

    // Stop slideshow
    function stopSlideshow() {
        if (!isSlideshow) return;

        isSlideshow = false;
        updateSlideshowButton();
        clearInterval(slideshowInterval);

        // Remove visual feedback
        document.body.classList.remove('slideshow-active');
    }

    // Update slideshow button state
    function updateSlideshowButton() {
        if (!slideshowButton) return;

        if (isSlideshow) {
            slideshowButton.innerHTML = '<i class="fas fa-pause"></i> Pause';
            slideshowButton.setAttribute('aria-label', 'Diashow pausieren');
            slideshowButton.classList.add('active');
        } else {
            slideshowButton.innerHTML = '<i class="fas fa-play"></i> Diashow';
            slideshowButton.setAttribute('aria-label', 'Diashow starten');
            slideshowButton.classList.remove('active');
        }
    }

    // Close lightbox
    function closeLightbox() {
        lightbox.classList.remove('active');
        lightbox.setAttribute('aria-hidden', 'true');
        document.body.style.overflow = '';
        stopSlideshow();

        // Pause any playing video
        const video = lightboxContent.querySelector('video');
        if (video) {
            video.pause();
        }

        // Return focus to the last focused element
        if (lastFocusedElement) {
            lastFocusedElement.focus();
        }
    }

    // Track the last focused element before opening lightbox
    let lastFocusedElement = null;

    // Helper functions
    function getCurrentCreatorName() {
        const creatorHeader = document.querySelector('.creator-info h1');
        return creatorHeader ? creatorHeader.textContent : 'Unknown';
    }

    function getMediaId() {
        if (!window.interfaceState.currentMediaData) return null;
        const data = window.interfaceState.currentMediaData;
        return `${data.creator_name}_${data.filename}`;
    }

    function updateButtonStates() {
        const mediaId = getMediaId();
        if (!mediaId) return;

        // Like Button
        const likeBtn = document.getElementById('likeButton');
        if (likeBtn) {
            if (window.interfaceState.likedItems.has(mediaId)) {
                likeBtn.classList.add('liked');
            } else {
                likeBtn.classList.remove('liked');
            }
        }

        // Bookmark Button
        const bookmarkBtn = document.getElementById('bookmarkButton');
        if (bookmarkBtn) {
            let isBookmarked = false;
            for (const listData of Object.values(window.interfaceState.bookmarkLists)) {
                if (listData.items && listData.items.has(mediaId)) {
                    isBookmarked = true;
                    break;
                }
            }

            if (isBookmarked) {
                bookmarkBtn.classList.add('bookmarked');
            } else {
                bookmarkBtn.classList.remove('bookmarked');
            }
        }
    }

    function toggleInterface() {
        interfaceHidden = !interfaceHidden;
        if (interfaceHidden) {
            lightbox.classList.add('interface-hidden');
        } else {
            lightbox.classList.remove('interface-hidden');
        }
    }

    function handleDoubleClick(e) {
        const currentTime = Date.now();
        if (currentTime - lastClickTime < 300) {
            // Double click detected
            e.preventDefault();
            toggleInterface();
        }
        lastClickTime = currentTime;
    }

    async function toggleLike() {
        const mediaId = getMediaId();
        if (!mediaId) return;

        const wasLiked = window.interfaceState.likedItems.has(mediaId);

        if (wasLiked) {
            window.interfaceState.likedItems.delete(mediaId);
        } else {
            window.interfaceState.likedItems.add(mediaId);
        }

        updateButtonStates();

        // Save to localStorage as fallback
        localStorage.setItem('likedItems', JSON.stringify([...window.interfaceState.likedItems]));

        console.log(wasLiked ? '❤️ Unliked:' : '❤️ Liked:', mediaId);
    }

    async function toggleBookmark() {
        const mediaId = getMediaId();
        if (!mediaId) return;

        // Simple implementation: add to default list
        if (!window.interfaceState.bookmarkLists['default']) {
            window.interfaceState.bookmarkLists['default'] = {
                name: 'Standard',
                items: new Set()
            };
        }

        const defaultList = window.interfaceState.bookmarkLists['default'];
        const wasBookmarked = defaultList.items.has(mediaId);

        if (wasBookmarked) {
            defaultList.items.delete(mediaId);
        } else {
            defaultList.items.add(mediaId);
        }

        updateButtonStates();

        // Save to localStorage as fallback
        const bookmarkData = {};
        for (const [listId, listData] of Object.entries(window.interfaceState.bookmarkLists)) {
            bookmarkData[listId] = {
                name: listData.name,
                items: [...listData.items]
            };
        }
        localStorage.setItem('bookmarkLists', JSON.stringify(bookmarkData));

        console.log(wasBookmarked ? '🔖 Unbookmarked:' : '🔖 Bookmarked:', mediaId);
    }

    // Load saved state from localStorage
    function loadSavedState() {
        try {
            const savedLikes = localStorage.getItem('likedItems');
            if (savedLikes) {
                const likesArray = JSON.parse(savedLikes);
                window.interfaceState.likedItems = new Set(likesArray);
            }

            const savedBookmarks = localStorage.getItem('bookmarkLists');
            if (savedBookmarks) {
                const bookmarkData = JSON.parse(savedBookmarks);
                for (const [listId, listData] of Object.entries(bookmarkData)) {
                    window.interfaceState.bookmarkLists[listId] = {
                        name: listData.name,
                        items: new Set(listData.items)
                    };
                }
            }
        } catch (error) {
            console.error('Error loading saved state:', error);
        }
    }

    // Event Listeners
    function initializeEventListeners() {
        // Media item click handlers
        document.addEventListener('click', function(e) {
            const mediaItem = e.target.closest('.media-item');
            if (mediaItem) {
                e.preventDefault();
                mediaArray = collectMediaItems();
                currentIndex = mediaArray.indexOf(mediaItem);

                if (currentIndex !== -1) {
                    lastFocusedElement = document.activeElement;
                    showMedia(currentIndex);
                    lightbox.classList.add('active');
                    lightbox.setAttribute('aria-hidden', 'false');
                    document.body.style.overflow = 'hidden';

                    // Set focus to close button for better keyboard navigation
                    setTimeout(() => lightboxClose.focus(), 100);
                }
            }
        });

        // Keyboard navigation for media items
        document.addEventListener('keydown', function(e) {
            const mediaItem = document.activeElement.closest('.media-item');
            if (mediaItem && (e.key === 'Enter' || e.key === ' ')) {
                e.preventDefault();
                mediaItem.click();
            }
        });

        // Navigation buttons
        if (prevButton) {
            prevButton.addEventListener('click', function(e) {
                e.stopPropagation();
                showMedia(currentIndex - 1);
            });
        }

        if (nextButton) {
            nextButton.addEventListener('click', function(e) {
                e.stopPropagation();
                showMedia(currentIndex + 1);
            });
        }

        // Slideshow button
        if (slideshowButton) {
            slideshowButton.addEventListener('click', function(e) {
                e.stopPropagation();
                if (isSlideshow) stopSlideshow();
                else startSlideshow();
            });
        }

        // Close button
        if (lightboxClose) {
            lightboxClose.addEventListener('click', function(e) {
                e.stopPropagation();
                closeLightbox();
            });
        }

        // Close when clicking outside content (with double-click detection)
        lightbox.addEventListener('click', function(e) {
            if (e.target === lightbox) {
                handleDoubleClick(e);
                // Only close if not a double-click
                setTimeout(() => {
                    if (Date.now() - lastClickTime > 300) {
                        closeLightbox();
                    }
                }, 300);
            }
        });

        // Double-click on content to toggle interface
        lightboxContent.addEventListener('click', handleDoubleClick);

        // Like button
        const likeBtn = document.getElementById('likeButton');
        if (likeBtn) {
            likeBtn.addEventListener('click', function(e) {
                e.stopPropagation();
                toggleLike();
            });
        }

        // Bookmark button
        const bookmarkBtn = document.getElementById('bookmarkButton');
        if (bookmarkBtn) {
            bookmarkBtn.addEventListener('click', function(e) {
                e.stopPropagation();
                toggleBookmark();
            });
        }

        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (!lightbox.classList.contains('active')) return;

            e.stopPropagation();

            switch(e.key) {
                case 'Escape':
                    closeLightbox();
                    break;
                case 'ArrowLeft':
                    showMedia(currentIndex - 1);
                    break;
                case 'ArrowRight':
                case ' ':
                    if (e.key === ' ') e.preventDefault();
                    showMedia(currentIndex + 1);
                    break;
                case 's':
                    e.preventDefault();
                    if (isSlideshow) stopSlideshow();
                    else startSlideshow();
                    break;
                case 'l':
                case 'L':
                    e.preventDefault();
                    toggleLike();
                    break;
                case 'b':
                case 'B':
                    e.preventDefault();
                    toggleBookmark();
                    break;
                case 'h':
                case 'H':
                    e.preventDefault();
                    toggleInterface();
                    break;
            }
        });

        // Handle tab key in lightbox
        lightbox.addEventListener('keydown', function(e) {
            if (e.key === 'Tab') {
                const focusableElements = lightbox.querySelectorAll(
                    'button:not([disabled]), [href], input:not([disabled]), select:not([disabled]), ' +
                    'textarea:not([disabled]), [tabindex]:not([tabindex="-1"]):not([disabled])'
                );

                const firstElement = focusableElements[0];
                const lastElement = focusableElements[focusableElements.length - 1];

                if (e.shiftKey && document.activeElement === firstElement) {
                    e.preventDefault();
                    lastElement.focus();
                } else if (!e.shiftKey && document.activeElement === lastElement) {
                    e.preventDefault();
                    firstElement.focus();
                }
            }
        });

        // Handle tab changes
        const tabButtons = document.querySelectorAll('[data-bs-toggle="tab"]');
        tabButtons.forEach(button => {
            button.addEventListener('shown.bs.tab', function() {
                if (lightbox.classList.contains('active')) {
                    const oldIndex = currentIndex;
                    mediaArray = collectMediaItems();
                    currentIndex = Math.min(oldIndex, mediaArray.length - 1);
                    showMedia(currentIndex);
                }
            });
        });

        // Touch/Swipe support for mobile devices
        let startX = 0;
        let startY = 0;

        lightboxContent.addEventListener('touchstart', function(e) {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
        }, { passive: true });

        lightboxContent.addEventListener('touchend', function(e) {
            if (!startX || !startY) return;

            const endX = e.changedTouches[0].clientX;
            const endY = e.changedTouches[0].clientY;

            const deltaX = endX - startX;
            const deltaY = endY - startY;

            // Only trigger if horizontal swipe is dominant
            if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
                if (deltaX > 0) {
                    // Swipe right - previous image
                    showMedia(currentIndex - 1);
                } else {
                    // Swipe left - next image
                    showMedia(currentIndex + 1);
                }
            }

            startX = 0;
            startY = 0;
        }, { passive: true });
    }

    // Initialize everything
    loadSavedState();
    initializeEventListeners();
});
