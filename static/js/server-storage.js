/**
 * Server-basierte Speicherung für Likes und Bookmarks
 * Ersetzt localStorage durch Server-API-Aufrufe
 */

// === LIKES API ===

async function loadLikesFromServer() {
    try {
        const response = await fetch('/api/likes');
        const result = await response.json();
        
        if (result.success) {
            window.interfaceState.likedItems = new Set(result.data);
            console.log('✅ Likes vom Server geladen:', result.data.length);
            return true;
        } else {
            console.error('❌ Fehler beim <PERSON>den der Likes:', result.error);
            return false;
        }
    } catch (error) {
        console.error('❌ Netzwerkfehler beim <PERSON>den der Likes:', error);
        return false;
    }
}

async function addLikeToServer(mediaId) {
    try {
        const response = await fetch(`/api/likes/${encodeURIComponent(mediaId)}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        const result = await response.json();
        
        if (result.success) {
            console.log('✅ Like auf Server gespeichert:', mediaId);
            return true;
        } else {
            console.error('❌ <PERSON><PERSON> beim <PERSON> des <PERSON>:', result.error);
            return false;
        }
    } catch (error) {
        console.error('❌ Netzwerkfehler beim Speichern des Likes:', error);
        return false;
    }
}

async function removeLikeFromServer(mediaId) {
    try {
        const response = await fetch(`/api/likes/${encodeURIComponent(mediaId)}`, {
            method: 'DELETE'
        });
        
        const result = await response.json();
        
        if (result.success) {
            console.log('✅ Like vom Server entfernt:', mediaId);
            return true;
        } else {
            console.error('❌ Fehler beim Entfernen des Likes:', result.error);
            return false;
        }
    } catch (error) {
        console.error('❌ Netzwerkfehler beim Entfernen des Likes:', error);
        return false;
    }
}

// === BOOKMARKS API ===

async function loadBookmarksFromServer() {
    try {
        const response = await fetch('/api/bookmarks');
        const result = await response.json();
        
        if (result.success) {
            // Konvertiere Arrays zurück zu Sets
            window.interfaceState.bookmarkLists = {};
            for (const [listId, listData] of Object.entries(result.data)) {
                window.interfaceState.bookmarkLists[listId] = {
                    name: listData.name,
                    items: new Set(listData.items)
                };
            }
            
            console.log('✅ Bookmarks vom Server geladen:', Object.keys(result.data).length, 'Listen');
            return true;
        } else {
            console.error('❌ Fehler beim Laden der Bookmarks:', result.error);
            return false;
        }
    } catch (error) {
        console.error('❌ Netzwerkfehler beim Laden der Bookmarks:', error);
        return false;
    }
}

async function createBookmarkListOnServer(name) {
    try {
        const response = await fetch('/api/bookmarks/lists', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ name: name })
        });
        
        const result = await response.json();
        
        if (result.success) {
            console.log('✅ Bookmark-Liste auf Server erstellt:', result.data);
            return result.data.list_id;
        } else {
            console.error('❌ Fehler beim Erstellen der Bookmark-Liste:', result.error);
            return null;
        }
    } catch (error) {
        console.error('❌ Netzwerkfehler beim Erstellen der Bookmark-Liste:', error);
        return null;
    }
}

async function deleteBookmarkListOnServer(listId) {
    try {
        const response = await fetch(`/api/bookmarks/lists/${encodeURIComponent(listId)}`, {
            method: 'DELETE'
        });
        
        const result = await response.json();
        
        if (result.success) {
            console.log('✅ Bookmark-Liste vom Server gelöscht:', listId);
            return true;
        } else {
            console.error('❌ Fehler beim Löschen der Bookmark-Liste:', result.error);
            return false;
        }
    } catch (error) {
        console.error('❌ Netzwerkfehler beim Löschen der Bookmark-Liste:', error);
        return false;
    }
}

async function addToBookmarkListOnServer(listId, mediaId) {
    try {
        const response = await fetch(`/api/bookmarks/lists/${encodeURIComponent(listId)}/${encodeURIComponent(mediaId)}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        const result = await response.json();
        
        if (result.success) {
            console.log('✅ Medium zur Bookmark-Liste auf Server hinzugefügt:', mediaId, 'zu', listId);
            return true;
        } else {
            console.error('❌ Fehler beim Hinzufügen zur Bookmark-Liste:', result.error);
            return false;
        }
    } catch (error) {
        console.error('❌ Netzwerkfehler beim Hinzufügen zur Bookmark-Liste:', error);
        return false;
    }
}

async function removeFromBookmarkListOnServer(listId, mediaId) {
    try {
        const response = await fetch(`/api/bookmarks/lists/${encodeURIComponent(listId)}/${encodeURIComponent(mediaId)}`, {
            method: 'DELETE'
        });
        
        const result = await response.json();
        
        if (result.success) {
            console.log('✅ Medium aus Bookmark-Liste auf Server entfernt:', mediaId, 'aus', listId);
            return true;
        } else {
            console.error('❌ Fehler beim Entfernen aus Bookmark-Liste:', result.error);
            return false;
        }
    } catch (error) {
        console.error('❌ Netzwerkfehler beim Entfernen aus Bookmark-Liste:', error);
        return false;
    }
}

// === INITIALISIERUNG ===

async function initServerStorage() {
    console.log('🔄 Initialisiere Server-Storage...');
    
    // Lade Likes und Bookmarks vom Server
    const likesLoaded = await loadLikesFromServer();
    const bookmarksLoaded = await loadBookmarksFromServer();
    
    // Erstelle Standard-Bookmark-Liste falls keine vorhanden
    if (bookmarksLoaded && Object.keys(window.interfaceState.bookmarkLists).length === 0) {
        console.log('📝 Erstelle Standard-Bookmark-Liste...');
        const listId = await createBookmarkListOnServer('Favoriten');
        if (listId) {
            window.interfaceState.bookmarkLists[listId] = {
                name: 'Favoriten',
                items: new Set()
            };
        }
    }
    
    console.log('✅ Server-Storage initialisiert');
    console.log(`📊 Likes: ${window.interfaceState.likedItems.size}`);
    console.log(`📊 Bookmark-Listen: ${Object.keys(window.interfaceState.bookmarkLists).length}`);
    
    return likesLoaded && bookmarksLoaded;
}

// === FALLBACK FÜR OFFLINE-MODUS ===

function fallbackToLocalStorage() {
    console.log('⚠️ Fallback zu localStorage aktiviert');
    
    // Lade aus localStorage falls Server nicht erreichbar
    try {
        const savedLikes = localStorage.getItem('likedItems');
        if (savedLikes) {
            window.interfaceState.likedItems = new Set(JSON.parse(savedLikes));
        }

        const savedBookmarkLists = localStorage.getItem('bookmarkLists');
        if (savedBookmarkLists) {
            const lists = JSON.parse(savedBookmarkLists);
            window.interfaceState.bookmarkLists = {};
            for (const [listId, listData] of Object.entries(lists)) {
                window.interfaceState.bookmarkLists[listId] = {
                    name: listData.name,
                    items: new Set(listData.items || [])
                };
            }
        }

        // Erstelle Standard-Liste falls keine vorhanden
        if (Object.keys(window.interfaceState.bookmarkLists).length === 0) {
            const listId = 'list_' + Date.now();
            window.interfaceState.bookmarkLists[listId] = {
                name: 'Favoriten',
                items: new Set()
            };
        }
    } catch (e) {
        console.warn('Fehler beim Laden aus localStorage:', e);
    }
}
