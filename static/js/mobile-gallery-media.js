// Media Loading Functionality

// Function to load a media element (image or video)
function loadMediaElement(element, src) {
    if (!element || !src) return;

    return new Promise((resolve) => {
        // If it's an image
        if (element.tagName === 'IMG') {
            const img = new Image();
            img.onload = () => {
                element.src = src;
                element.classList.remove('lazy-load');
                resolve();
            };
            img.onerror = () => {
                console.error('Error loading image:', src);
                resolve();
            };
            img.src = src;
        }
        // If it's a video
        else if (element.tagName === 'VIDEO') {
            const source = element.querySelector('source');
            if (source) {
                source.src = src;
                element.load();
                element.classList.remove('lazy-load');
                // For videos, we'll consider them loaded when they can play
                const onCanPlay = () => {
                    element.removeEventListener('canplay', onCanPlay);
                    resolve();
                };
                element.addEventListener('canplay', onCanPlay);
                element.addEventListener('error', () => {
                    console.error('Error loading video:', src);
                    element.removeEventListener('canplay', onCanPlay);
                    resolve();
                });
            } else {
                resolve();
            }
        } else {
            resolve();
        }
    });
}

async function loadMoreMedia(limit = 5) { // Reduzierte Standard-Batch-Größe für bessere Performance
    console.log(`🔄 loadMoreMedia called with limit: ${limit}`);

    if (window.isLoading) {
        console.log('⏳ Loading already in progress...');
        return;
    }

    window.isLoading = true;
    console.log('🔒 Set isLoading to true');

    const loadingIndicator = document.querySelector('.loading-indicator');
    if (loadingIndicator) {
        loadingIndicator.style.display = 'flex';
        console.log('👁️ Loading indicator shown');
    } else {
        console.log('❌ Loading indicator not found');
    }

    try {
        const offset = window.totalLoaded || 0;
        const url = `/api/media/batch?offset=${offset}&limit=${limit}&fast=true`; // Fast mode
        console.log(`🌐 Fetching media from ${url}`);
        console.log(`📊 Current state: totalLoaded=${window.totalLoaded}, totalItems=${window.totalItems}`);

        const startTime = performance.now();
        const response = await fetch(url, {
            headers: {
                'Cache-Control': 'max-age=300', // 5 Minuten Cache für bessere Performance
                'Accept': 'application/json'
            }
        });

        const fetchTime = performance.now() - startTime;
        console.log(`⚡ API fetch took: ${fetchTime.toFixed(2)}ms`);

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`HTTP error! Status: ${response.status}, Response: ${errorText}`);
        }

        const result = await response.json();
        console.log('📦 API Response for /api/media/batch:', result);

        if (!result.success) {
            throw new Error(result.error || 'Error loading media: Invalid response from server');
        }

        const container = document.querySelector('.swipe-container');
        if (!container) {
            throw new Error('Container element not found');
        }
        console.log('📦 Container found:', container);

        // Check if we have data and items in the response
        if (!result.data || !Array.isArray(result.data.items)) {
            throw new Error('Invalid response format: missing or invalid items array');
        }

        const items = result.data.items;
        console.log('Number of items received from API:', items.length); // DEBUG

        if (items.length === 0) {
            console.log('No more items to load from API');
            window.totalItems = window.totalLoaded; // Update total items to prevent further loading
            return;
        }

        // Create document fragment for better performance
        const fragment = document.createDocumentFragment();

        // Create media elements for each item
        items.forEach((item, index) => {
            try {
                const itemElement = createMediaElement(item, window.totalLoaded + index);
                if (itemElement) {
                    fragment.appendChild(itemElement);
                }
            } catch (error) {
                console.error('Error creating media element:', error, item);
            }
        });

        // Append all new items at once
        container.appendChild(fragment);
        console.log('Container innerHTML after appending items:', container.innerHTML); // DEBUG

        // Update counts
        const itemsAdded = items.length;
        window.totalLoaded += itemsAdded;
        container.setAttribute('data-loaded', window.totalLoaded);

        // Update the total items if provided in the response
        if (result.data.total !== undefined) {
            window.totalItems = parseInt(result.data.total, 10);
            container.setAttribute('data-total', window.totalItems);
        }

        console.log(`Successfully loaded ${itemsAdded} items. Total loaded: ${window.totalLoaded} of ${window.totalItems || 'unknown'}`);

        // Initialize lazy loading for new items
        if (typeof window.initLazyLoading === 'function') {
            window.initLazyLoading();
        }

        // Show first item if we have any
        if (window.totalLoaded === items.length && items.length > 0) {
            console.log('Showing first item...');
            if (typeof window.showItem === 'function') {
                window.showItem(0);
            }
        }

        // DEAKTIVIERT: Automatisches Nachladen um endloses Laden zu verhindern
        // If we have very few items and there are more, load more to fill the view (but prevent infinite loops)
        if (window.totalLoaded < 3 && result.data.has_more && !window.preventAutoLoad && !window.ULTRAFAST_MODE) {
            window.preventAutoLoad = true; // Prevent infinite loading
            const additionalItemsNeeded = Math.min(3, 3 - window.totalLoaded);
            console.log(`Loading ${additionalItemsNeeded} additional items to fill the view...`);
            setTimeout(() => {
                window.preventAutoLoad = false;
                window.loadMoreMedia(additionalItemsNeeded);
            }, 500);
        }

    } catch (error) {
        console.error('Error loading more media:', error);

        // Show error to user
        showErrorNotification(`Error loading content: ${error.message || 'Unknown error'}`);

    } finally {
        window.isLoading = false;
        const loadingIndicator = document.querySelector('.loading-indicator');
        if (loadingIndicator) loadingIndicator.style.display = 'none';
    }
}

/**
 * Shows an error notification to the user
 * @param {string} message - The error message to display
 */
function showErrorNotification(message) {
    // Remove any existing error notifications
    const existingErrors = document.querySelectorAll('.error-notification');
    existingErrors.forEach(el => el.remove());

    // Create and style the error element
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-notification';

    // Apply styles
    errorDiv.style.position = 'fixed';
    errorDiv.style.bottom = '20px';
    errorDiv.style.left = '50%';
    errorDiv.style.transform = 'translateX(-50%)';
    errorDiv.style.padding = '12px 24px';
    errorDiv.style.borderRadius = '25px';
    errorDiv.style.backgroundColor = '#f8d7da';
    errorDiv.style.color = '#721c24';
    errorDiv.style.border = '1px solid #f5c6cb';
    errorDiv.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.1)';
    errorDiv.style.zIndex = '1000';
    errorDiv.style.maxWidth = '90%';
    errorDiv.style.textAlign = 'center';
    errorDiv.style.fontSize = '14px';
    errorDiv.style.fontWeight = '500';
    errorDiv.style.transition = 'opacity 0.3s ease';
    errorDiv.style.opacity = '0';
    errorDiv.style.cursor = 'pointer';

    // Set the error message
    errorDiv.textContent = message.length > 100 ? `${message.substring(0, 97)}...` : message;

    // Add to the document
    document.body.appendChild(errorDiv);

    // Trigger reflow to enable the opacity transition
    errorDiv.offsetHeight;
    errorDiv.style.opacity = '1';

    // Auto-remove after 5 seconds
    const removeTimeout = setTimeout(() => {
        errorDiv.style.opacity = '0';
        setTimeout(() => {
            if (document.body.contains(errorDiv)) {
                document.body.removeChild(errorDiv);
            }
        }, 300);
    }, 5000);

    // Allow manual dismissal
    errorDiv.addEventListener('click', () => {
        clearTimeout(removeTimeout);
        errorDiv.style.opacity = '0';
        setTimeout(() => {
            if (document.body.contains(errorDiv)) {
                document.body.removeChild(errorDiv);
            }
        }, 300);
    });
}

function checkLoadMore(index) {
    const items = document.querySelectorAll('.swipe-item');
    const loadThreshold = Math.max(1, items.length - 1); // Weniger aggressives Preloading

    // Überprüfe auf die globale isLoading Variable oder setze sie auf false, wenn nicht vorhanden
    const isLoading = window.isLoading || false;
    const totalLoaded = window.totalLoaded || 0;
    const totalItems = window.totalItems || 0;

    console.log(`CheckLoadMore: index=${index}, threshold=${loadThreshold}, loaded=${totalLoaded}, total=${totalItems}, isLoading=${isLoading}`);

    // DEAKTIVIERT: Automatisches Preloading um endloses Laden zu verhindern
    // Lade mehr Medien nur wenn wir wirklich am Ende sind und nicht bereits laden
    if (index >= loadThreshold && !isLoading && totalLoaded < totalItems && !window.preventAutoLoad && !window.ULTRAFAST_MODE) {
        console.log('Triggering loadMoreMedia due to proximity to end');
        window.preventAutoLoad = true;
        setTimeout(() => {
            loadMoreMedia(2); // Kleinere Batch-Größe
            setTimeout(() => {
                window.preventAutoLoad = false;
            }, 1000);
        }, 200);
    }
}

function createMediaElement(item, index) {
    if (!item || !item.path) return null;

    const itemElement = document.createElement('div');
    itemElement.className = 'swipe-item';
    itemElement.dataset.index = index;

    // Debug-Ausgabe
    console.log('Verarbeite Medienpfad:', item.path);
    console.log('Thumbnail-Pfad:', item.thumbnail_path);

    // Bereinige den Medienpfad
    let mediaPath = item.path || '';

    // Debug-Ausgabe
    console.log('Originaler Pfad:', mediaPath);

    // Entferne führende Schrägstriche und 'public/' falls vorhanden
    mediaPath = mediaPath.replace(/^\/+|\/+$/g, '').replace(/^public\//, '');

    // Korrigiere 'mediacretors' zu 'media/cretors'
    mediaPath = mediaPath.replace(/^mediacretors\//, 'media/cretors/');

    // Wenn der Pfad bereits mit 'http' oder 'data:' beginnt, direkt verwenden
    if (mediaPath.match(/^(http|data:)/)) {
        // Keine Änderung notwendig
    }
    // Ansonsten als relativen Pfad zum media-Endpunkt behandeln
    else {
        mediaPath = `/media/${mediaPath}`;
    }

    // Doppelte Schrägstriche bereinigen
    mediaPath = mediaPath.replace(/([^:]\/)\/+/g, '$1');

    console.log('Verarbeiteter Medienpfad:', mediaPath);

    // Thumbnail-Pfad anpassen, falls vorhanden
    if (item.thumbnail_path) {
        // Bereinige den Thumbnail-Pfad
        let thumbPath = (item.thumbnail_path || '').replace(/^\/+|\/+$/g, '');

        console.log('Originaler Thumbnail-Pfad:', thumbPath);

        // Korrigiere 'mediacretors' zu 'media/cretors' auch in Thumbnail-Pfaden
        thumbPath = thumbPath.replace(/^mediacretors\//, 'media/cretors/');

        // Wenn der Thumbnail-Pfad bereits mit 'http' oder 'data:' beginnt, direkt verwenden
        if (thumbPath.match(/^(http|data:)/)) {
            // Keine Änderung notwendig
        }
        // Ansonsten als relativen Pfad zum media/thumbnails-Endpunkt behandeln
        else if (thumbPath) {
            thumbPath = thumbPath.replace(/^thumbnails\//, '');
            thumbPath = `/media/thumbnails/${thumbPath}`;
        }

        console.log('Berechneter Thumbnail-Pfad:', thumbPath);
        item.thumbnail_path = thumbPath;
    }

    // Determine if the item is a video based on the type or file extension
    const isVideo = item.type === 'video' || item.path.toLowerCase().match(/\.(mp4|webm|ogg)$/);
    const mediaId = `media-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

    // Create the media container
    const mediaContainer = document.createElement('div');
    mediaContainer.className = 'media-container';

    // Create TikTok-style UI overlay
    const tiktokUI = document.createElement('div');
    tiktokUI.className = 'tiktok-ui';

    // Create sidebar with action buttons
    const sidebar = document.createElement('div');
    sidebar.className = 'tiktok-sidebar';

    // Like button
    const likeBtn = document.createElement('button');
    likeBtn.className = 'tiktok-action-btn';
    likeBtn.innerHTML = '<i class="fas fa-heart"></i>';
    likeBtn.onclick = () => toggleLike(likeBtn);

    // Share button
    const shareBtn = document.createElement('button');
    shareBtn.className = 'tiktok-action-btn';
    shareBtn.innerHTML = '<i class="fas fa-share"></i>';
    shareBtn.onclick = () => shareMedia(item);

    // Info button
    const infoBtn = document.createElement('button');
    infoBtn.className = 'tiktok-action-btn';
    infoBtn.innerHTML = '<i class="fas fa-info"></i>';
    infoBtn.onclick = () => toggleInfo(itemElement);

    sidebar.appendChild(likeBtn);
    sidebar.appendChild(shareBtn);
    sidebar.appendChild(infoBtn);

    // Create bottom info section
    const bottomInfo = document.createElement('div');
    bottomInfo.className = 'tiktok-bottom-info';

    const creatorInfo = document.createElement('div');
    creatorInfo.className = 'creator-info';

    const creatorAvatar = document.createElement('div');
    creatorAvatar.className = 'creator-avatar';
    creatorAvatar.textContent = (item.creator_name || item.creator_id || 'U')[0].toUpperCase();

    const creatorName = document.createElement('div');
    creatorName.className = 'creator-name';
    creatorName.textContent = item.creator_name || item.creator_id || 'Unknown';

    creatorInfo.appendChild(creatorAvatar);
    creatorInfo.appendChild(creatorName);

    // Add description if available
    const description = document.createElement('div');
    description.className = 'media-description';
    description.textContent = item.metadata?.description || item.metadata?.prompt || '';

    bottomInfo.appendChild(creatorInfo);
    if (description.textContent) {
        bottomInfo.appendChild(description);
    }

    tiktokUI.appendChild(sidebar);
    tiktokUI.appendChild(bottomInfo);

    // Create a placeholder for the loading indicator
    const placeholder = document.createElement('div');
    placeholder.className = 'media-loading-indicator';
    placeholder.innerHTML = '<div class="spinner"></div>';

    // Create the media element (image or video)
    const mediaElement = document.createElement(isVideo ? 'video' : 'img');
    mediaElement.className = `media-content ${isVideo ? 'video-content' : 'lazy-load-image'}`;
    mediaElement.alt = item.creator_name || item.creator_id || 'Media';
    mediaElement.loading = 'lazy';
    mediaElement.dataset.index = index;

    // Set the thumbnail as the initial source for faster loading
    if (item.thumbnail_path) {
        mediaElement.dataset.src = mediaPath; // Full resolution path
        mediaElement.src = item.thumbnail_path; // Thumbnail path
        mediaElement.dataset.full = mediaPath; // For lazy loading
    } else {
        mediaElement.src = mediaPath; // Fallback to full path
    }

    if (isVideo) {
        mediaElement.preload = 'metadata';
        mediaElement.playsInline = true;
        mediaElement.muted = true;
        mediaElement.loop = true;
        mediaElement.controls = false;

        // Set the video source
        const source = document.createElement('source');
        source.src = mediaPath;
        source.type = `video/${mediaPath.split('.').pop()}`;
        mediaElement.appendChild(source);

        // Add a play button
        const playButton = document.createElement('div');
        playButton.className = 'play-button';
        playButton.innerHTML = '<i class="fas fa-play"></i>';

        // Add video progress bar
        const progressBar = document.createElement('div');
        progressBar.className = 'video-progress';

        // Video event listeners
        mediaElement.addEventListener('loadedmetadata', () => {
            placeholder.style.display = 'none';
        });

        mediaElement.addEventListener('timeupdate', () => {
            if (mediaElement.duration) {
                const progress = (mediaElement.currentTime / mediaElement.duration) * 100;
                progressBar.style.transform = `scaleX(${progress / 100})`;
            }
        });

        mediaElement.addEventListener('play', () => {
            playButton.style.display = 'none';
        });

        mediaElement.addEventListener('pause', () => {
            playButton.style.display = 'flex';
        });

        mediaElement.addEventListener('ended', () => {
            playButton.style.display = 'flex';
            progressBar.style.transform = 'scaleX(0)';
        });

        // Click to play/pause
        mediaContainer.addEventListener('click', (e) => {
            // Don't interfere with UI buttons
            if (e.target.closest('.tiktok-ui')) return;

            if (mediaElement.paused) {
                mediaElement.play().catch(console.error);
            } else {
                mediaElement.pause();
            }
        });

        mediaContainer.appendChild(placeholder);
        mediaContainer.appendChild(mediaElement);
        mediaContainer.appendChild(playButton);
        mediaContainer.appendChild(progressBar);
    } else {
        // For images: Use lazy loading with thumbnail and full image
        const hasThumbnail = item.thumbnail_path && item.thumbnail_path !== '';

        if (hasThumbnail) {
            // If we have a thumbnail, load it first
            mediaElement.src = item.thumbnail_path;
            // Store the full resolution path in data-src for later loading
            mediaElement.dataset.src = mediaPath;
        } else {
            // If no thumbnail, load the full image directly
            mediaElement.src = mediaPath;
        }

        // When the image (or thumbnail) loads
        mediaElement.onload = function() {
            // Hide the placeholder with a smooth transition
            placeholder.style.opacity = '0';
            setTimeout(() => {
                placeholder.style.display = 'none';
            }, 300);

            // If we have a full resolution image to load
            if (mediaElement.dataset.src && mediaElement.src !== mediaElement.dataset.src) {
                const fullImage = new Image();
                fullImage.onload = function() {
                    // Replace the thumbnail with the full resolution image
                    mediaElement.src = this.src;
                    mediaElement.removeAttribute('data-src');
                };
                fullImage.onerror = function() {
                    console.error('Error loading full resolution image:', mediaPath);
                };
                fullImage.src = mediaElement.dataset.src;
            }

            // Check if we need to load more media
            if (typeof checkLoadMore === 'function') {
                checkLoadMore(index);
            }
        };

        // Handle image loading errors
        mediaElement.onerror = function() {
            console.error('Error loading image:', hasThumbnail ? 'thumbnail' : 'full image',
                         hasThumbnail ? item.thumbnail_path : mediaPath);

            // If thumbnail failed, try loading the full image
            if (hasThumbnail && mediaElement.src !== mediaPath) {
                mediaElement.src = mediaPath;
                mediaElement.onerror = function() {
                    console.error('Error loading full image after thumbnail failed:', mediaPath);
                    placeholder.innerHTML = '<div class="media-error">Could not load image</div>';
                };
            } else {
                placeholder.innerHTML = '<div class="media-error">Could not load image</div>';
            }
        };

        mediaContainer.appendChild(placeholder);
        mediaContainer.appendChild(mediaElement);
    }

    // Add TikTok UI overlay to the item
    itemElement.appendChild(mediaContainer);
    itemElement.appendChild(tiktokUI);

    return itemElement;
}

// Toggle metadata visibility
function toggleMetadata(button) {
    const container = button.nextElementSibling;
    if (container && container.classList.contains('metadata-container')) {
        container.classList.toggle('visible');
        button.innerHTML = container.classList.contains('visible') ?
            '<i class="fas fa-times"></i> Close' :
            '<i class="fas fa-info-circle"></i> Info';
    }
}

// TikTok-style interaction functions
function toggleLike(button) {
    const isLiked = button.classList.contains('liked');
    if (isLiked) {
        button.classList.remove('liked');
        button.innerHTML = '<i class="fas fa-heart"></i>';
        button.style.color = 'white';
    } else {
        button.classList.add('liked');
        button.innerHTML = '<i class="fas fa-heart"></i>';
        button.style.color = '#ff3040';

        // Add heart animation
        button.style.transform = 'scale(1.2)';
        setTimeout(() => {
            button.style.transform = 'scale(1)';
        }, 200);
    }
}

function shareMedia(item) {
    if (navigator.share) {
        navigator.share({
            title: `Check out this ${item.type} by ${item.creator_name}`,
            text: item.metadata?.description || item.metadata?.prompt || '',
            url: window.location.href
        }).catch(err => console.log('Error sharing:', err));
    } else {
        // Fallback: copy to clipboard
        const url = window.location.href;
        navigator.clipboard.writeText(url).then(() => {
            showNotification('Link copied to clipboard!');
        }).catch(() => {
            showNotification('Unable to copy link');
        });
    }
}

function toggleInfo(itemElement) {
    const metadataOverlay = itemElement.querySelector('.metadata-overlay');
    if (metadataOverlay) {
        const isVisible = metadataOverlay.style.display !== 'none';
        metadataOverlay.style.display = isVisible ? 'none' : 'block';
    }
}

function showNotification(message) {
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 12px 24px;
        border-radius: 20px;
        z-index: 1000;
        font-size: 14px;
        backdrop-filter: blur(10px);
    `;
    notification.textContent = message;
    document.body.appendChild(notification);

    setTimeout(() => {
        notification.remove();
    }, 2000);
}

// Update progress indicator
function updateProgressIndicator() {
    const progressIndicator = document.getElementById('progressIndicator');
    const totalItems = window.totalItems || 0;
    const currentIndex = window.currentIndex || 0;

    if (!progressIndicator || totalItems <= 1) {
        if (progressIndicator) progressIndicator.style.display = 'none';
        return;
    }

    progressIndicator.style.display = 'flex';
    progressIndicator.innerHTML = '';

    // Show max 10 dots
    const maxDots = Math.min(10, totalItems);
    const step = Math.max(1, Math.floor(totalItems / maxDots));

    for (let i = 0; i < maxDots; i++) {
        const dot = document.createElement('div');
        dot.className = 'progress-dot';

        const dotIndex = i * step;
        if (Math.abs(currentIndex - dotIndex) <= step / 2) {
            dot.classList.add('active');
        }

        progressIndicator.appendChild(dot);
    }
}

// Make all necessary functions globally available
window.loadMoreMedia = loadMoreMedia;
window.checkLoadMore = checkLoadMore;
window.toggleMetadata = toggleMetadata;
window.loadMediaElement = loadMediaElement;
window.createMediaElement = createMediaElement;
window.toggleLike = toggleLike;
window.shareMedia = shareMedia;
window.toggleInfo = toggleInfo;
window.showNotification = showNotification;
window.updateProgressIndicator = updateProgressIndicator;

// Initial load when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    console.log('🚀 mobile-gallery-media.js: DOMContentLoaded, initiating first media load.');

    // Prüfe ob Ultra-Fast-Modus aktiv ist
    if (window.ULTRAFAST_MODE) {
        console.log('⚡ Ultra-Fast mode detected, skipping regular media loading');
        return;
    }

    // Debug: Check if container exists
    const container = document.getElementById('swipeContainer');
    console.log('📦 Container found:', container);

    // Initialize global variables
    window.totalLoaded = 0;
    window.totalItems = 0;
    window.isLoading = false;
    window.preventAutoLoad = false;

    if (typeof loadMoreMedia === 'function') {
        console.log('📞 Calling loadMoreMedia...');
        loadMoreMedia(3); // Load initial batch
    } else {
        console.error('❌ loadMoreMedia function is not defined at the point of DOMContentLoaded.');
    }
});

console.log('TikTok-style mobile media module initialized and functions exposed');
