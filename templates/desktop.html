{% extends "base.html" %}

{% block title %}CiitAI-Galerie - Desktop-Ansicht{% endblock %}

{% block extra_css %}
<style>
    .creator-card {
        transition: transform 0.3s;
        height: 100%;
    }
    .creator-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    .preview-images {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-gap: 5px;
        margin-bottom: 15px;
    }
    .preview-image {
        width: 100%;
        height: 120px;
        object-fit: cover;
        border-radius: 4px;
    }
    .creator-stats {
        display: flex;
        justify-content: space-between;
    }
    .stat-item {
        display: flex;
        align-items: center;
    }
    .stat-item i {
        margin-right: 5px;
    }

    /* Media Grid for Likes/Bookmarks */
    .media-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        grid-gap: 15px;
    }
    .media-item {
        position: relative;
        height: 200px;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 3px 8px rgba(0,0,0,0.1);
        cursor: pointer;
    }
    .media-item img, .media-item video {
        width: 100%;
        height: 100%;
        object-fit: contain;
        background: #000;
        transition: transform 0.3s;
    }
    .media-item:hover img, .media-item:hover video {
        transform: scale(1.05);
    }

    /* Lightbox Styles */
    .lightbox {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.95);
        z-index: 1000;
        justify-content: center;
        align-items: center;
        opacity: 0;
        transition: opacity 0.3s ease;
        backdrop-filter: blur(5px);
    }

    .lightbox.active {
        display: flex;
        opacity: 1;
    }

    .lightbox-content {
        max-width: 95%;
        max-height: 95%;
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        transition: transform 0.3s ease;
    }

    .lightbox-media {
        max-width: 100%;
        max-height: 90vh;
        border-radius: 4px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        transition: all 0.3s ease;
        object-fit: contain;
        background: #000;
    }

    .lightbox-close {
        position: fixed;
        top: 25px;
        right: 25px;
        color: white;
        font-size: 28px;
        cursor: pointer;
        z-index: 1010;
        width: 44px;
        height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 50%;
        border: 2px solid rgba(255, 255, 255, 0.2);
        transition: all 0.2s ease;
    }

    .lightbox-close:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: scale(1.1);
    }

    .lightbox-nav {
        position: fixed;
        width: 100%;
        display: flex;
        justify-content: space-between;
        top: 50%;
        transform: translateY(-50%);
        padding: 0 20px;
        z-index: 1010;
        pointer-events: none;
    }

    .lightbox-nav button {
        background: rgba(0, 0, 0, 0.6);
        color: white;
        border: none;
        width: 44px;
        height: 44px;
        border-radius: 50%;
        cursor: pointer;
        font-size: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        pointer-events: auto;
        transition: all 0.2s ease;
        backdrop-filter: blur(5px);
        border: 2px solid rgba(255, 255, 255, 0.1);
    }

    .lightbox-nav button:hover {
        background: rgba(0, 0, 0, 0.8);
        transform: scale(1.1);
    }

    .lightbox-controls {
        position: fixed;
        bottom: 30px;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        gap: 15px;
        z-index: 1010;
        background: rgba(0, 0, 0, 0.6);
        padding: 8px 20px;
        border-radius: 50px;
        backdrop-filter: blur(5px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }

    .lightbox-controls button {
        background: rgba(255, 255, 255, 0.15);
        color: white;
        border: none;
        padding: 8px 20px;
        cursor: pointer;
        border-radius: 50px;
        font-size: 14px;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 8px;
        transition: all 0.2s ease;
    }

    .lightbox-controls button:hover {
        background: rgba(255, 255, 255, 0.25);
        transform: translateY(-2px);
    }

    .lightbox-controls button.active {
        background: white;
        color: #333;
    }

    .lightbox-controls button.liked {
        background: #ff4757;
        color: white;
    }

    .lightbox-controls button.bookmarked {
        background: #3742fa;
        color: white;
    }

    .lightbox-counter {
        position: fixed;
        top: 25px;
        left: 25px;
        color: white;
        font-size: 14px;
        font-weight: 500;
        z-index: 1010;
        background: rgba(0, 0, 0, 0.6);
        padding: 6px 14px;
        border-radius: 50px;
        backdrop-filter: blur(5px);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    /* Interface Hidden State */
    .lightbox.interface-hidden .lightbox-close,
    .lightbox.interface-hidden .lightbox-counter,
    .lightbox.interface-hidden .lightbox-nav,
    .lightbox.interface-hidden .lightbox-controls {
        opacity: 0;
        pointer-events: none;
        transition: opacity 0.3s ease;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="mb-0">Creator-Galerie</h1>
    <div class="btn-group" role="group">
        <button type="button" class="btn btn-outline-primary" id="showLikesBtn">
            <i class="fas fa-heart"></i> Gelikte Medien
        </button>
        <div class="btn-group">
            <button type="button" class="btn btn-outline-primary" id="showBookmarksBtn">
                <i class="fas fa-bookmark"></i> Bookmarks
            </button>
            <button type="button" class="btn btn-outline-primary dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false" id="bookmarkDropdown" style="display: none;">
                <span class="visually-hidden">Toggle Dropdown</span>
            </button>
            <ul class="dropdown-menu" id="bookmarkDropdownMenu">
                <!-- Wird dynamisch gefüllt -->
            </ul>
        </div>
    </div>
</div>

<!-- Likes/Bookmarks Container (initially hidden) -->
<div id="specialViewContainer" style="display: none;">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h2 id="specialViewTitle">Gelikte Medien</h2>
        <button type="button" class="btn btn-outline-secondary" id="backToCreatorsBtn">
            <i class="fas fa-arrow-left"></i> Zurück zu Creators
        </button>
    </div>
    <div id="specialMediaGrid" class="media-grid">
        <!-- Content will be populated by JavaScript -->
    </div>
</div>

<!-- Creators Container -->
<div id="creatorsContainer">

<div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
    {% for creator in creators %}
        <div class="col">
            <div class="card creator-card shadow-sm h-100">
                <div class="card-body">
                    <h5 class="card-title">{{ creator.name }}</h5>

                    {% if creator.description %}
                    <p class="card-text text-muted small mb-3">{{ creator.description }}</p>
                    {% endif %}

                    <div class="preview-images">
                        {% set previews = get_random_previews(creator, 4) %}
                        {% for original_path, thumbnail_path in previews %}
                            <img src="{{ url_for('static', filename=thumbnail_path) }}" alt="Preview" class="preview-image" data-original="{{ url_for('serve_media', filename='cretors/' + '/'.join(original_path.split('/cretors/')[1:])) }}">
                        {% else %}
                            <div class="text-center p-4 bg-light w-100">
                                <i class="fas fa-images fa-2x text-muted"></i>
                                <p class="text-muted mt-2">Keine Vorschaubilder</p>
                            </div>
                        {% endfor %}
                    </div>

                    <div class="creator-stats mt-3">
                        <div class="stat-item">
                            <i class="fas fa-image text-primary"></i>
                            <span>{{ creator.image_count }} Bilder</span>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-video text-danger"></i>
                            <span>{{ creator.video_count }} Videos</span>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-transparent border-0">
                    <a href="{{ url_for('creator_detail', creator_id=creator.id) }}" class="btn btn-primary btn-sm w-100">
                        Details anzeigen
                    </a>
                </div>
            </div>
        </div>
    {% else %}
        <div class="col-12">
            <div class="alert alert-info">
                Keine Creator gefunden. Bitte fügen Sie Creator-Ordner zum Verzeichnis hinzu.
            </div>
        </div>
    {% endfor %}
</div>

</div> <!-- End creatorsContainer -->

<!-- Lightbox für Vollbildanzeige -->
<div class="lightbox" id="mediaLightbox">
    <div class="lightbox-close">&times;</div>
    <div class="lightbox-counter">1 / 10</div>
    <div class="lightbox-nav">
        <button id="prevButton"><i class="fas fa-chevron-left"></i></button>
        <button id="nextButton"><i class="fas fa-chevron-right"></i></button>
    </div>
    <div class="lightbox-content">
        <!-- Inhalt wird dynamisch eingefügt -->
    </div>
    <div class="lightbox-controls" id="lightboxControls">
        <button id="likeButton"><i class="fas fa-heart"></i> Like</button>
        <button id="bookmarkButton"><i class="fas fa-bookmark"></i> Bookmark</button>
        <button id="slideshowButton"><i class="fas fa-play"></i> Diashow</button>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/lightbox.js') }}"></script>
<script src="{{ url_for('static', filename='js/desktop-bookmarks.js') }}"></script>
{% endblock %}
