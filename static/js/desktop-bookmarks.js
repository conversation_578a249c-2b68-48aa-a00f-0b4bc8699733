/**
 * Desktop Bookmarks and Likes functionality
 * Handles display of liked media and bookmarks in desktop view
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize interface state if not already done
    window.interfaceState = window.interfaceState || {
        likedItems: new Set(),
        bookmarkLists: {},
        currentView: 'creators'
    };

    // Load saved state
    loadSavedState();

    // Update dropdown on page load
    setTimeout(() => {
        updateBookmarkDropdown();
    }, 100);

    // Get DOM elements
    const showLikesBtn = document.getElementById('showLikesBtn');
    const showBookmarksBtn = document.getElementById('showBookmarksBtn');
    const backToCreatorsBtn = document.getElementById('backToCreatorsBtn');
    const specialViewContainer = document.getElementById('specialViewContainer');
    const creatorsContainer = document.getElementById('creatorsContainer');
    const specialViewTitle = document.getElementById('specialViewTitle');
    const specialMediaGrid = document.getElementById('specialMediaGrid');
    const bookmarkDropdown = document.getElementById('bookmarkDropdown');
    const bookmarkDropdownMenu = document.getElementById('bookmarkDropdownMenu');

    // Event listeners
    if (showLikesBtn) {
        showLikesBtn.addEventListener('click', showLikedMedia);
    }

    if (showBookmarksBtn) {
        showBookmarksBtn.addEventListener('click', showBookmarkedMedia);
    }

    if (backToCreatorsBtn) {
        backToCreatorsBtn.addEventListener('click', showCreators);
    }

    // Load saved state from localStorage
    function loadSavedState() {
        try {
            const savedLikes = localStorage.getItem('likedItems');
            if (savedLikes) {
                const likesArray = JSON.parse(savedLikes);
                window.interfaceState.likedItems = new Set(likesArray);
            }

            const savedBookmarks = localStorage.getItem('bookmarkLists');
            if (savedBookmarks) {
                const bookmarkData = JSON.parse(savedBookmarks);
                for (const [listId, listData] of Object.entries(bookmarkData)) {
                    window.interfaceState.bookmarkLists[listId] = {
                        name: listData.name,
                        items: new Set(listData.items)
                    };
                }
            }
        } catch (error) {
            console.error('Error loading saved state:', error);
        }
    }

    // Show liked media
    async function showLikedMedia() {
        window.interfaceState.currentView = 'likes';
        specialViewTitle.textContent = 'Gelikte Medien';

        // Update button states
        showLikesBtn.classList.add('active');
        showBookmarksBtn.classList.remove('active');

        // Show special view container
        creatorsContainer.style.display = 'none';
        specialViewContainer.style.display = 'block';

        if (window.interfaceState.likedItems.size === 0) {
            specialMediaGrid.innerHTML = `
                <div class="col-12">
                    <div class="alert alert-info text-center">
                        <i class="fas fa-heart fa-3x mb-3 text-muted"></i>
                        <h4>Keine gelikten Medien</h4>
                        <p>Liken Sie Medien in der Galerie, um sie hier zu sehen.</p>
                    </div>
                </div>
            `;
            return;
        }

        // Load media details from server
        try {
            const mediaIds = [...window.interfaceState.likedItems];
            const response = await fetch('/api/media/details', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    media_ids: mediaIds
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            const result = await response.json();

            if (result.success && result.data && result.data.items) {
                displayMediaItems(result.data.items);
            } else {
                throw new Error('Invalid API response');
            }
        } catch (error) {
            console.error('Error loading liked media:', error);
            specialMediaGrid.innerHTML = `
                <div class="col-12">
                    <div class="alert alert-danger text-center">
                        <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                        <h4>Fehler beim Laden</h4>
                        <p>Gelikte Medien konnten nicht geladen werden.</p>
                    </div>
                </div>
            `;
        }
    }

    // Show bookmarked media
    async function showBookmarkedMedia() {
        window.interfaceState.currentView = 'bookmarks';
        specialViewTitle.textContent = 'Bookmark-Listen';

        // Update button states
        showBookmarksBtn.classList.add('active');
        showLikesBtn.classList.remove('active');

        // Show special view container
        creatorsContainer.style.display = 'none';
        specialViewContainer.style.display = 'block';

        if (Object.keys(window.interfaceState.bookmarkLists).length === 0) {
            specialMediaGrid.innerHTML = `
                <div class="col-12">
                    <div class="alert alert-info text-center">
                        <i class="fas fa-bookmark fa-3x mb-3 text-muted"></i>
                        <h4>Keine Bookmark-Listen</h4>
                        <p>Erstellen Sie Bookmark-Listen in der Galerie, um sie hier zu sehen.</p>
                    </div>
                </div>
            `;
            return;
        }

        // Show bookmark lists overview instead of all media
        displayBookmarkLists();
        updateBookmarkDropdown();
    }

    // Update bookmark dropdown menu
    function updateBookmarkDropdown() {
        if (!bookmarkDropdown || !bookmarkDropdownMenu) return;

        const bookmarkLists = Object.entries(window.interfaceState.bookmarkLists);

        if (bookmarkLists.length === 0) {
            bookmarkDropdown.style.display = 'none';
            // Reset main button text
            if (showBookmarksBtn) {
                showBookmarksBtn.innerHTML = `<i class="fas fa-bookmark"></i> Bookmarks`;
            }
            return;
        }

        // Show dropdown if there are lists
        bookmarkDropdown.style.display = 'inline-block';

        // Update main button text with count
        if (showBookmarksBtn) {
            const totalLists = bookmarkLists.length;
            const totalItems = bookmarkLists.reduce((sum, [, listData]) => sum + listData.items.size, 0);
            showBookmarksBtn.innerHTML = `
                <i class="fas fa-bookmark"></i> Bookmarks
                <span class="badge bg-primary ms-1">${totalLists}</span>
            `;
        }

        // Build dropdown menu
        const dropdownHtml = bookmarkLists.map(([listId, listData]) => {
            const itemCount = listData.items.size;
            const listName = listData.name || 'Unbenannte Liste';

            return `
                <li>
                    <a class="dropdown-item" href="#" onclick="showBookmarkListContent('${listId}')">
                        <i class="fas fa-bookmark me-2"></i>
                        ${listName}
                        <span class="badge bg-secondary ms-2">${itemCount}</span>
                    </a>
                </li>
            `;
        }).join('');

        bookmarkDropdownMenu.innerHTML = `
            <li><h6 class="dropdown-header">Bookmark-Listen</h6></li>
            ${dropdownHtml}
            <li><hr class="dropdown-divider"></li>
            <li>
                <a class="dropdown-item" href="#" onclick="showBookmarkedMedia()">
                    <i class="fas fa-th-large me-2"></i>
                    Alle Listen anzeigen
                </a>
            </li>
        `;
    }

    // Display bookmark lists as cards
    async function displayBookmarkLists() {
        // Get thumbnails for each list
        const listsWithThumbnails = await Promise.all(
            Object.entries(window.interfaceState.bookmarkLists).map(async ([listId, listData]) => {
                const itemCount = listData.items.size;
                const listName = listData.name || 'Unbenannte Liste';
                let thumbnailUrl = null;

                // Get first media item as thumbnail
                if (listData.items.size > 0) {
                    const firstMediaId = [...listData.items][0];
                    try {
                        const response = await fetch('/api/media/details', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ media_ids: [firstMediaId] })
                        });

                        if (response.ok) {
                            const result = await response.json();
                            if (result.success && result.data.items.length > 0) {
                                const item = result.data.items[0];
                                thumbnailUrl = item.thumbnail_path ? `/media/${item.thumbnail_path}` : `/media/${item.path}`;
                            }
                        }
                    } catch (error) {
                        console.error('Error loading thumbnail for list:', listId, error);
                    }
                }

                return { listId, listData, listName, itemCount, thumbnailUrl };
            })
        );

        const listsHtml = listsWithThumbnails.map(({ listId, listName, itemCount, thumbnailUrl }) => {
            const thumbnailContent = thumbnailUrl ?
                `<img src="${thumbnailUrl}" class="card-img-top bookmark-list-thumbnail" alt="${listName}">` :
                `<div class="card-img-top bookmark-list-placeholder">
                    <i class="fas fa-bookmark fa-3x text-primary"></i>
                </div>`;

            return `
                <div class="col-md-6 col-lg-4 col-xl-3 mb-4">
                    <div class="card bookmark-list-card h-100" data-list-id="${listId}">
                        ${thumbnailContent}
                        <div class="card-body text-center">
                            <h5 class="card-title">${listName}</h5>
                            <p class="card-text text-muted">${itemCount} ${itemCount === 1 ? 'Medium' : 'Medien'}</p>
                            <button class="btn btn-primary btn-sm" onclick="showBookmarkListContent('${listId}')">
                                <i class="fas fa-eye"></i> Anzeigen
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }).join('');

        specialMediaGrid.innerHTML = `
            <div class="row">
                ${listsHtml}
            </div>
        `;

        // Add CSS for bookmark list cards if not already added
        if (!document.getElementById('bookmarkListStyles')) {
            const style = document.createElement('style');
            style.id = 'bookmarkListStyles';
            style.textContent = `
                .bookmark-list-card {
                    cursor: pointer;
                    transition: transform 0.2s ease, box-shadow 0.2s ease;
                }
                .bookmark-list-card:hover {
                    transform: translateY(-5px);
                    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
                }
                .bookmark-list-thumbnail {
                    height: 200px;
                    object-fit: contain;
                    background: #000;
                }
                .bookmark-list-placeholder {
                    height: 200px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background: var(--bs-gray-100);
                }
            `;
            document.head.appendChild(style);
        }
    }

    // Show content of a specific bookmark list
    async function showBookmarkListContent(listId) {
        const listData = window.interfaceState.bookmarkLists[listId];
        if (!listData) {
            console.error('Bookmark list not found:', listId);
            return;
        }

        specialViewTitle.innerHTML = `
            <div class="d-flex align-items-center">
                <button class="btn btn-outline-secondary btn-sm me-3" onclick="showBookmarkedMedia()">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <span>🔖 ${listData.name}</span>
            </div>
        `;

        if (listData.items.size === 0) {
            specialMediaGrid.innerHTML = `
                <div class="col-12">
                    <div class="alert alert-info text-center">
                        <i class="fas fa-bookmark fa-3x mb-3 text-muted"></i>
                        <h4>Liste ist leer</h4>
                        <p>Diese Bookmark-Liste enthält noch keine Medien.</p>
                    </div>
                </div>
            `;
            return;
        }

        // Load media details from server
        try {
            const mediaIds = [...listData.items];
            const response = await fetch('/api/media/details', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    media_ids: mediaIds
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            const result = await response.json();

            if (result.success && result.data && result.data.items) {
                displayMediaItems(result.data.items);
            } else {
                throw new Error('Invalid API response');
            }
        } catch (error) {
            console.error('Error loading bookmark list content:', error);
            specialMediaGrid.innerHTML = `
                <div class="col-12">
                    <div class="alert alert-danger text-center">
                        <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                        <h4>Fehler beim Laden</h4>
                        <p>Bookmark-Liste konnte nicht geladen werden.</p>
                    </div>
                </div>
            `;
        }
    }

    // Display media items in grid
    function displayMediaItems(mediaItems) {
        if (!mediaItems || mediaItems.length === 0) {
            specialMediaGrid.innerHTML = `
                <div class="col-12">
                    <div class="alert alert-warning text-center">
                        <h4>Keine Medien gefunden</h4>
                        <p>Die ausgewählten Medien konnten nicht gefunden werden.</p>
                    </div>
                </div>
            `;
            return;
        }

        const mediaHtml = mediaItems.map((item, index) => {
            const mediaPath = `/media/${item.path}`;
            const thumbnailPath = item.thumbnail_path ? `/media/${item.thumbnail_path}` : mediaPath;
            const typeIcon = item.type === 'video' ? 'fas fa-video' : 'fas fa-image';

            return `
                <div class="media-item" data-type="${item.type}" data-path="${mediaPath}" data-index="${index}">
                    <img src="${thumbnailPath}" alt="${item.type === 'video' ? 'Video-Vorschau' : 'Bild'}">
                    <div class="media-type-indicator" style="position: absolute; top: 10px; right: 10px; width: 30px; height: 30px; border-radius: 50%; background-color: rgba(0,0,0,0.6); color: white; display: flex; align-items: center; justify-content: center;">
                        <i class="${typeIcon}"></i>
                    </div>
                </div>
            `;
        }).join('');

        specialMediaGrid.innerHTML = mediaHtml;

        // Add click handlers for lightbox
        const mediaElements = specialMediaGrid.querySelectorAll('.media-item');
        mediaElements.forEach((element, index) => {
            element.addEventListener('click', () => {
                // Initialize lightbox with current media array
                if (window.openLightbox) {
                    window.openLightbox(mediaElements, index);
                }
            });
        });
    }

    // Show creators view
    function showCreators() {
        window.interfaceState.currentView = 'creators';

        // Update button states
        showLikesBtn.classList.remove('active');
        showBookmarksBtn.classList.remove('active');

        // Show creators container
        specialViewContainer.style.display = 'none';
        creatorsContainer.style.display = 'block';
    }

    // Make functions globally available for onclick handlers
    window.showBookmarkListContent = showBookmarkListContent;
    window.showBookmarkedMedia = showBookmarkedMedia;
});
