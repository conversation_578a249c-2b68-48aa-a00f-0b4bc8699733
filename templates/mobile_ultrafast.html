<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CiitAI-Galerie - Ultra-Fast Mobile</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body, html {
            height: 100%;
            overflow: hidden;
            background: #000;
            font-family: Arial, sans-serif;
            -webkit-user-select: none;
            user-select: none;
        }

        .swipe-container {
            position: relative;
            width: 100%;
            height: 100vh;
            background: #000;
            touch-action: pan-y;
            -webkit-overflow-scrolling: touch;
        }

        .swipe-item {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
            display: none;
            align-items: center;
            justify-content: center;
        }

        .loading-indicator {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: #000;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            color: white;
            font-size: 24px;
        }

        .spinner {
            width: 30px;
            height: 30px;
            border: 3px solid #333;
            border-top: 3px solid #fff;
            border-radius: 50%;
            animation: spin 0.8s linear infinite;
            margin-right: 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Optimiert für Touch */
        img, video {
            max-width: 100%;
            max-height: 100vh;
            object-fit: contain;
            user-select: none;
            -webkit-user-select: none;
            pointer-events: none;
        }

        video {
            pointer-events: auto;
        }

        /* Performance-optimierte TikTok-style Animationen */
        .swipe-item {
            will-change: transform, opacity;
            transform: translateZ(0);
            transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94), opacity 0.3s ease;
            backface-visibility: hidden;
            -webkit-backface-visibility: hidden;
        }

        /* Smooth swipe transitions */
        .swipe-item.transitioning {
            transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94), opacity 0.3s ease;
        }

        .swipe-item.no-transition {
            transition: none;
        }

        /* Ultra-Fast Debug Info */
        .ultrafast-debug {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: #00ff00;
            padding: 8px;
            border-radius: 4px;
            font-size: 11px;
            z-index: 2000;
            font-family: monospace;
        }

        /* Swipe-Hinweise */
        .swipe-hints {
            position: fixed;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(255,255,255,0.3);
            font-size: 12px;
            z-index: 100;
            pointer-events: none;
            text-align: center;
        }

        /* Preload-Indikator */
        .preload-indicator {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 8px;
            height: 8px;
            background: #00ff00;
            border-radius: 50%;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .preload-indicator.active {
            opacity: 1;
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.5); }
        }

        /* TikTok-style Sidebar */
        .tiktok-sidebar {
            position: fixed;
            right: 12px;
            bottom: 120px;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 20px;
            pointer-events: auto;
        }

        .tiktok-action-btn {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: rgba(0, 0, 0, 0.6);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            user-select: none;
        }

        .tiktok-action-btn:hover {
            background: rgba(0, 0, 0, 0.8);
            transform: scale(1.1);
        }

        .tiktok-action-btn:active {
            transform: scale(0.95);
        }

        .tiktok-action-btn.active {
            background: rgba(255, 69, 58, 0.8);
            border-color: rgba(255, 69, 58, 0.8);
        }

        .tiktok-action-btn.liked {
            background: rgba(255, 69, 58, 0.9);
            color: #fff;
            animation: heartBeat 0.6s ease;
        }

        @keyframes heartBeat {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.3); }
        }

        /* Top Controls */
        .top-controls {
            position: fixed;
            top: 20px;
            left: 20px;
            right: 20px;
            z-index: 1000;
            display: flex;
            justify-content: space-between;
            align-items: center;
            pointer-events: auto;
        }

        /* Bottom Media Type Filter */
        .bottom-media-filter {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            display: flex;
            background: rgba(0, 0, 0, 0.7);
            border-radius: 25px;
            padding: 8px;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            gap: 4px;
            pointer-events: auto;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .media-filter-btn {
            width: 48px;
            height: 48px;
            border: none;
            background: transparent;
            color: rgba(255, 255, 255, 0.6);
            border-radius: 50%;
            font-size: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .media-filter-btn.active {
            background: rgba(255, 69, 58, 0.3);
            color: #ff453a;
            border: 2px solid #ff453a;
            transform: scale(1.1);
        }

        .media-filter-btn:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            transform: scale(1.05);
        }

        .media-filter-btn:active {
            transform: scale(0.95);
        }

        .creator-info {
            background: rgba(0, 0, 0, 0.6);
            border-radius: 20px;
            padding: 8px 16px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .creator-info:hover {
            background: rgba(0, 0, 0, 0.8);
        }

        /* Info Overlay */
        .info-overlay {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
            color: white;
            padding: 60px 20px 20px;
            z-index: 500;
            transform: translateY(100%);
            transition: transform 0.3s ease;
            pointer-events: auto;
        }

        .info-overlay.visible {
            transform: translateY(0);
        }

        .info-content {
            max-height: 40vh;
            overflow-y: auto;
        }

        .info-item {
            margin-bottom: 12px;
            font-size: 14px;
        }

        .info-label {
            color: rgba(255, 255, 255, 0.7);
            font-weight: 500;
            margin-right: 8px;
        }

        .info-value {
            color: white;
        }

        /* Navigation Menu */
        .nav-menu {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1500;
            pointer-events: auto;
        }



        .nav-menu-btn {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: rgba(0, 0, 0, 0.6);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .nav-menu-btn:hover {
            background: rgba(0, 0, 0, 0.8);
            transform: scale(1.1);
        }

        .nav-dropdown {
            position: absolute;
            top: 60px;
            left: 0;
            min-width: 200px;
            background: rgba(0, 0, 0, 0.9);
            border-radius: 12px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 8px;
            display: none;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
        }

        .nav-dropdown.visible {
            display: block;
            animation: slideDown 0.3s ease;
        }

        @keyframes slideDown {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .nav-item {
            padding: 12px 16px;
            color: white;
            cursor: pointer;
            border-radius: 8px;
            transition: all 0.2s ease;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .nav-item:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .nav-item.active {
            background: rgba(255, 69, 58, 0.3);
            border-left: 3px solid #ff453a;
        }

        .nav-divider {
            height: 1px;
            background: rgba(255, 255, 255, 0.2);
            margin: 8px 0;
        }

        /* Bookmark Lists Modal */
        .bookmark-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            z-index: 2000;
            display: none;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10px);
        }

        .bookmark-modal.visible {
            display: flex;
        }

        .bookmark-modal-content {
            background: rgba(20, 20, 20, 0.95);
            border-radius: 16px;
            padding: 24px;
            max-width: 400px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .bookmark-modal-header {
            color: white;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            text-align: center;
        }

        .bookmark-list-item {
            padding: 12px 16px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            margin-bottom: 8px;
            color: white;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .bookmark-list-item:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .bookmark-list-name {
            flex: 1;
        }

        .bookmark-list-count {
            color: rgba(255, 255, 255, 0.7);
            font-size: 12px;
        }

        .new-list-input {
            width: 100%;
            padding: 12px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            color: white;
            font-size: 14px;
            margin-bottom: 12px;
        }

        .new-list-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .modal-buttons {
            display: flex;
            gap: 8px;
            margin-top: 16px;
        }

        .modal-btn {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .modal-btn.primary {
            background: #ff453a;
            color: white;
        }

        .modal-btn.primary:hover {
            background: #ff6b5a;
        }

        .modal-btn.secondary {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .modal-btn.secondary:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        /* Mehr laden Button */
        .load-more-btn {
            position: fixed;
            bottom: 100px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            z-index: 1000;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
        }

        .load-more-btn:hover {
            background: rgba(255, 71, 87, 0.8);
            transform: translateX(-50%) scale(1.05);
            box-shadow: 0 6px 25px rgba(255, 71, 87, 0.4);
        }

        /* Empty State */
        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            color: white;
            text-align: center;
            padding: 40px;
        }

        .empty-icon {
            font-size: 80px;
            margin-bottom: 20px;
            opacity: 0.6;
        }

        .empty-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 10px;
            opacity: 0.8;
        }

        .empty-subtitle {
            font-size: 16px;
            opacity: 0.6;
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <!-- Navigation Menu -->
    <div class="nav-menu">
        <div class="nav-menu-btn" id="navMenuBtn">
            ☰
        </div>
        <div class="nav-dropdown" id="navDropdown">
            <div class="nav-item" id="navHome">
                <span>🏠</span>
                <span>Alle Medien</span>
            </div>
            <div class="nav-divider"></div>
            <div class="nav-item" id="navLikes">
                <span>❤️</span>
                <span>Gelikte Medien</span>
            </div>
            <div class="nav-item" id="navBookmarks">
                <span>🔖</span>
                <span>Bookmark Listen</span>
            </div>
            <div class="nav-divider"></div>
            <div class="nav-item" id="navCreateList">
                <span>➕</span>
                <span>Neue Liste erstellen</span>
            </div>
        </div>
    </div>

    <!-- Top Controls -->
    <div class="top-controls">
        <!-- Current Creator Info -->
        <div class="creator-info" id="creatorInfo">
            <span id="creatorName">Alle Creator</span>
        </div>
    </div>

    <!-- Main Container -->
    <div class="swipe-container" id="swipeContainer">
        <!-- Loading Indicator -->
        <div class="loading-indicator" id="loadingIndicator">
            <div class="spinner"></div>
            <span>Ultra-Fast Loading...</span>
        </div>

        <!-- Swipe Hints -->
        <div class="swipe-hints">
            ↑<br>
            SWIPE<br>
            ↓
        </div>

        <!-- Preload Indicator -->
        <div class="preload-indicator" id="preloadIndicator"></div>
    </div>

    <!-- TikTok-style Sidebar -->
    <div class="tiktok-sidebar">
        <!-- Like Button -->
        <div class="tiktok-action-btn" id="likeBtn" title="Like">
            ❤️
        </div>

        <!-- Bookmark Button -->
        <div class="tiktok-action-btn" id="bookmarkBtn" title="Bookmark">
            🔖
        </div>

        <!-- Info Button -->
        <div class="tiktok-action-btn" id="infoBtn" title="Info">
            ℹ️
        </div>

        <!-- Creator Filter Button -->
        <div class="tiktok-action-btn" id="creatorFilterBtn" title="Nur dieser Creator">
            🎯
        </div>
    </div>

    <!-- Bottom Media Type Filter -->
    <div class="bottom-media-filter">
        <button class="media-filter-btn active" data-type="all" title="Alle Medien">
            🎬
        </button>
        <button class="media-filter-btn" data-type="image" title="Nur Bilder">
            📷
        </button>
        <button class="media-filter-btn" data-type="video" title="Nur Videos">
            🎥
        </button>
    </div>

    <!-- Info Overlay -->
    <div class="info-overlay" id="infoOverlay">
        <div class="info-content" id="infoContent">
            <!-- Info wird dynamisch geladen -->
        </div>
    </div>

    <!-- Bookmark Lists Modal -->
    <div class="bookmark-modal" id="bookmarkModal">
        <div class="bookmark-modal-content">
            <div class="bookmark-modal-header" id="bookmarkModalHeader">
                Zu Liste hinzufügen
            </div>
            <div id="bookmarkListsContainer">
                <!-- Listen werden dynamisch geladen -->
            </div>
            <input type="text" class="new-list-input" id="newListInput" placeholder="Neue Liste erstellen..." style="display: none;">
            <div class="modal-buttons">
                <button class="modal-btn secondary" id="bookmarkModalCancel">Abbrechen</button>
                <button class="modal-btn primary" id="bookmarkModalConfirm" style="display: none;">Erstellen</button>
            </div>
        </div>
    </div>

    <!-- Ultra-Fast Debug Info -->
    <div class="ultrafast-debug" id="ultrafastDebug">
        <div>Loaded: <span id="loadedCount">0</span></div>
        <div>Buffer: <span id="bufferCount">0</span></div>
        <div>Current: <span id="currentIndex">0</span></div>
        <div>Loading: <span id="loadingStatus">No</span></div>
    </div>

    <!-- JavaScript -->
    <!-- Server Storage für Likes und Bookmarks -->
    <script src="{{ url_for('static', filename='js/server-storage.js') }}"></script>
    <!-- Ultra-Fast Mobile Gallery Script -->
    <script src="{{ url_for('static', filename='js/mobile-gallery-ultrafast.js') }}"></script>

    <script>
        // Ultra-Fast Debug und Monitoring
        const DEBUG = true;

        if (DEBUG) {
            // Update Debug-Info sehr häufig für Echtzeit-Feedback
            setInterval(() => {
                const items = document.querySelectorAll('.swipe-item');
                const remaining = items.length - (window.currentIndex || 0);

                document.getElementById('loadedCount').textContent = window.totalLoaded || 0;
                document.getElementById('bufferCount').textContent = remaining;
                document.getElementById('currentIndex').textContent = (window.currentIndex || 0) + 1;
                document.getElementById('loadingStatus').textContent = window.isLoading ? 'Yes' : 'No';

                // Preload-Indikator
                const indicator = document.getElementById('preloadIndicator');
                if (window.isLoading) {
                    indicator.classList.add('active');
                } else {
                    indicator.classList.remove('active');
                }

                // Warnung bei niedrigem Buffer
                const debug = document.getElementById('ultrafastDebug');
                if (remaining <= 5) {
                    debug.style.background = 'rgba(255,0,0,0.8)';
                    debug.style.color = '#fff';
                } else if (remaining <= 10) {
                    debug.style.background = 'rgba(255,165,0,0.8)';
                    debug.style.color = '#fff';
                } else {
                    debug.style.background = 'rgba(0,0,0,0.8)';
                    debug.style.color = '#00ff00';
                }
            }, 100); // Sehr häufige Updates

            // Keyboard-Navigation für Desktop-Testing
            document.addEventListener('keydown', (e) => {
                const items = document.querySelectorAll('.swipe-item');

                if (e.key === 'ArrowUp' && window.currentIndex > 0) {
                    window.showItem(window.currentIndex - 1);
                } else if (e.key === 'ArrowDown' && window.currentIndex < items.length - 1) {
                    window.showItem(window.currentIndex + 1);
                } else if (e.key === 'r') {
                    location.reload();
                } else if (e.key === 'p') {
                    // Manuelles Preload
                    if (typeof window.loadMoreMedia === 'function') {
                        window.loadMoreMedia(10);
                    }
                }
            });
        }

        // Performance-Monitoring
        let loadStartTime = performance.now();

        window.addEventListener('load', () => {
            const loadTime = performance.now() - loadStartTime;
            console.log(`⚡ Ultra-Fast page loaded in: ${loadTime.toFixed(2)}ms`);
        });

        // Swipe-Performance-Tracking
        let swipeCount = 0;
        let swipeStartTime = Date.now();

        document.addEventListener('touchend', () => {
            swipeCount++;
            if (swipeCount % 10 === 0) {
                const avgTime = (Date.now() - swipeStartTime) / swipeCount;
                console.log(`📊 Average swipe response: ${avgTime.toFixed(2)}ms`);
            }
        });

        // Error-Handling
        window.addEventListener('error', (e) => {
            console.error('🚨 Ultra-Fast error:', e.error);
        });

        console.log('⚡ Ultra-Fast Mobile Gallery page loaded');


    </script>
</body>
</html>
