{% extends "base.html" %}

{% block title %}{{ creator.name }} - CiitAI-Galerie{% endblock %}

{% block extra_css %}
<style>
    .media-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        grid-gap: 15px;
    }
    .media-item {
        position: relative;
        height: 200px;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 3px 8px rgba(0,0,0,0.1);
    }
    .media-item img, .media-item video {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s;
    }
    .media-item:hover img, .media-item:hover video {
        transform: scale(1.05);
    }

    .media-item {
        cursor: pointer;
    }

    /* Lightbox für Vollbildansicht */
    .lightbox {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.95);
        z-index: 1000;
        justify-content: center;
        align-items: center;
        opacity: 0;
        transition: opacity 0.3s ease;
        backdrop-filter: blur(5px);
    }

    .lightbox.active {
        display: flex;
        opacity: 1;
    }

    .lightbox-content {
        max-width: 95%;
        max-height: 95%;
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        transition: transform 0.3s ease;
    }

    .lightbox-media {
        max-width: 100%;
        max-height: 90vh;
        border-radius: 4px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        transition: all 0.3s ease;
    }

    .lightbox-content img.lightbox-media {
        object-fit: contain;
    }

    .lightbox-close {
        position: fixed;
        top: 25px;
        right: 25px;
        color: white;
        font-size: 28px;
        cursor: pointer;
        z-index: 1010;
        width: 44px;
        height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 50%;
        border: 2px solid rgba(255, 255, 255, 0.2);
        transition: all 0.2s ease;
    }

    .lightbox-close:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: scale(1.1);
    }

    .lightbox-nav {
        position: fixed;
        width: 100%;
        display: flex;
        justify-content: space-between;
        top: 50%;
        transform: translateY(-50%);
        padding: 0 20px;
        z-index: 1010;
        pointer-events: none;
    }

    .lightbox-nav button {
        background: rgba(0, 0, 0, 0.6);
        color: white;
        border: none;
        width: 44px;
        height: 44px;
        border-radius: 50%;
        cursor: pointer;
        font-size: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        pointer-events: auto;
        transition: all 0.2s ease;
        backdrop-filter: blur(5px);
        border: 2px solid rgba(255, 255, 255, 0.1);
    }

    .lightbox-nav button:hover {
        background: rgba(0, 0, 0, 0.8);
        transform: scale(1.1);
    }

    .lightbox-nav button:active {
        transform: scale(0.95);
    }

    .lightbox-controls {
        position: fixed;
        bottom: 30px;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        gap: 15px;
        z-index: 1010;
        background: rgba(0, 0, 0, 0.6);
        padding: 8px 20px;
        border-radius: 50px;
        backdrop-filter: blur(5px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }

    .lightbox-controls button {
        background: rgba(255, 255, 255, 0.15);
        color: white;
        border: none;
        padding: 8px 20px;
        cursor: pointer;
        border-radius: 50px;
        font-size: 14px;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 8px;
        transition: all 0.2s ease;
    }

    .lightbox-controls button:hover {
        background: rgba(255, 255, 255, 0.25);
        transform: translateY(-2px);
    }

    .lightbox-controls button:active {
        transform: translateY(0);
    }

    .lightbox-controls button.active {
        background: white;
        color: #333;
    }

    /* Like/Bookmark Button States */
    .lightbox-controls button.liked {
        background: #ff4757;
        color: white;
    }

    .lightbox-controls button.bookmarked {
        background: #3742fa;
        color: white;
    }

    /* Interface Hidden State */
    .lightbox.interface-hidden .lightbox-close,
    .lightbox.interface-hidden .lightbox-counter,
    .lightbox.interface-hidden .lightbox-nav,
    .lightbox.interface-hidden .lightbox-controls {
        opacity: 0;
        pointer-events: none;
        transition: opacity 0.3s ease;
    }

    .lightbox.interface-hidden .lightbox-controls {
        transform: translateX(-50%) translateY(20px);
    }

    /* Load More Button - always visible */
    .lightbox .load-more-btn {
        position: fixed;
        bottom: 30px;
        right: 30px;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        border: none;
        padding: 12px 20px;
        border-radius: 25px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        backdrop-filter: blur(5px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.2s ease;
        z-index: 1020;
        opacity: 1 !important;
        pointer-events: auto !important;
    }

    .lightbox .load-more-btn:hover {
        background: rgba(0, 0, 0, 0.9);
        transform: translateY(-2px);
    }

    .lightbox-counter {
        position: fixed;
        top: 25px;
        left: 25px;
        color: white;
        font-size: 14px;
        font-weight: 500;
        z-index: 1010;
        background: rgba(0, 0, 0, 0.6);
        padding: 6px 14px;
        border-radius: 50px;
        backdrop-filter: blur(5px);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    /* Loading spinner */
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .spinner-border {
        width: 2.5rem;
        height: 2.5rem;
        border-width: 0.25em;
        animation: spin 0.75s linear infinite;
    }

    /* Slideshow active state */
    body.slideshow-active .lightbox-media {
        box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
    }
    .media-type-indicator {
        position: absolute;
        top: 10px;
        right: 10px;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background-color: rgba(0,0,0,0.6);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .media-tabs {
        margin-bottom: 20px;
    }
    .creator-header {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
    }
    .creator-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background-color: #eee;
        margin-right: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 30px;
        color: #666;
    }
    .creator-info {
        flex: 1;
    }
    .stats-container {
        display: flex;
        gap: 20px;
        margin: 15px 0;
    }
    .stat-box {
        background-color: #f8f9fa;
        padding: 10px 15px;
        border-radius: 8px;
        text-align: center;
        min-width: 100px;
    }
    .stat-number {
        font-size: 1.5rem;
        font-weight: bold;
    }
    .stat-label {
        font-size: 0.9rem;
        color: #666;
    }
</style>
{% endblock %}

{% block content %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{{ url_for('index') }}">Home</a></li>
        <li class="breadcrumb-item active" aria-current="page">{{ creator.name }}</li>
    </ol>
</nav>

<div class="creator-header">
    <div class="creator-avatar">
        <i class="fas fa-user"></i>
    </div>
    <div class="creator-info">
        <h1>{{ creator.name }}</h1>
        {% if creator.description %}
            <p class="text-muted">{{ creator.description }}</p>
        {% endif %}

        <div class="stats-container">
            <div class="stat-box">
                <div class="stat-number">{{ creator.image_count }}</div>
                <div class="stat-label">Bilder</div>
            </div>
            <div class="stat-box">
                <div class="stat-number">{{ creator.video_count }}</div>
                <div class="stat-label">Videos</div>
            </div>
        </div>
    </div>
</div>

<ul class="nav nav-tabs media-tabs" id="mediaTab" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="all-tab" data-bs-toggle="tab" data-bs-target="#all" type="button" role="tab" aria-controls="all" aria-selected="true">
            Alle Medien
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="images-tab" data-bs-toggle="tab" data-bs-target="#images" type="button" role="tab" aria-controls="images" aria-selected="false">
            Bilder
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="videos-tab" data-bs-toggle="tab" data-bs-target="#videos" type="button" role="tab" aria-controls="videos" aria-selected="false">
            Videos
        </button>
    </li>
</ul>

<div class="tab-content" id="mediaTabContent">
    <div class="tab-pane fade show active" id="all" role="tabpanel" aria-labelledby="all-tab">
        {% if creator.all_images or creator.all_videos %}
            <div class="media-grid">
                {% for image_path in creator.all_images %}
                    {% set thumbnail_path = get_media_thumbnail(image_path) %}
                    {% set relative_path = image_path.split('public/')[1] if 'public/' in image_path else image_path %}
                    <div class="media-item" data-type="image" data-path="{{ url_for('serve_media', filename=relative_path) }}">
                        <img src="{{ url_for('static', filename=thumbnail_path) }}" alt="Bild">
                        <div class="media-type-indicator">
                            <i class="fas fa-image"></i>
                        </div>
                    </div>
                {% endfor %}

                {% for video_path in creator.all_videos %}
                    {% set thumbnail_path = get_media_thumbnail(video_path) %}
                    {% set relative_path = video_path.split('public/')[1] if 'public/' in video_path else video_path %}
                    <div class="media-item" data-type="video" data-path="{{ url_for('serve_media', filename=relative_path) }}">
                        <img src="{{ url_for('static', filename=thumbnail_path) }}" alt="Video-Vorschau">
                        <div class="media-type-indicator">
                            <i class="fas fa-video"></i>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <p class="text-center text-muted mt-4">Keine Medien gefunden</p>
        {% endif %}
    </div>

    <div class="tab-pane fade" id="images" role="tabpanel" aria-labelledby="images-tab">
        {% if creator.all_images %}
            <div class="media-grid">
                {% for image_path in creator.all_images %}
                    {% set thumbnail_path = get_media_thumbnail(image_path) %}
                    {% set relative_path = image_path.split('public/')[1] if 'public/' in image_path else image_path %}
                    <div class="media-item" data-type="image" data-path="{{ url_for('serve_media', filename=relative_path) }}">
                        <img src="{{ url_for('static', filename=thumbnail_path) }}" alt="Bild">
                        <div class="media-type-indicator">
                            <i class="fas fa-image"></i>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <p class="text-center text-muted mt-4">Keine Bilder gefunden</p>
        {% endif %}
    </div>

    <div class="tab-pane fade" id="videos" role="tabpanel" aria-labelledby="videos-tab">
        {% if creator.all_videos %}
            <div class="media-grid">
                {% for video_path in creator.all_videos %}
                    {% set thumbnail_path = get_media_thumbnail(video_path) %}
                    {% set relative_path = video_path.split('public/')[1] if 'public/' in video_path else video_path %}
                    <div class="media-item" data-type="video" data-path="{{ url_for('serve_media', filename=relative_path) }}">
                        <img src="{{ url_for('static', filename=thumbnail_path) }}" alt="Video-Vorschau">
                        <div class="media-type-indicator">
                            <i class="fas fa-video"></i>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <p class="text-center text-muted mt-4">Keine Videos gefunden</p>
        {% endif %}
    </div>
</div>

<!-- Lightbox für Vollbildanzeige -->
<div class="lightbox" id="mediaLightbox">
    <div class="lightbox-close">&times;</div>
    <div class="lightbox-counter">1 / 10</div>
    <div class="lightbox-nav">
        <button id="prevButton"><i class="fas fa-chevron-left"></i></button>
        <button id="nextButton"><i class="fas fa-chevron-right"></i></button>
    </div>
    <div class="lightbox-content">
        <!-- Inhalt wird dynamisch eingefügt -->
    </div>
    <div class="lightbox-controls" id="lightboxControls">
        <button id="likeButton"><i class="fas fa-heart"></i> Like</button>
        <button id="bookmarkButton"><i class="fas fa-bookmark"></i> Bookmark</button>
        <button id="slideshowButton"><i class="fas fa-play"></i> Diashow</button>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/lightbox.js') }}"></script>
{% endblock %}
