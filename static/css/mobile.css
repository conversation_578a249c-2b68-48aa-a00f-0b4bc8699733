/* TikTok-Style Mobile Gallery */
html, body {
    height: 100%;
    margin: 0;
    padding: 0;
    background: #000;
    color: #fff;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    overscroll-behavior: none;
    overflow: hidden;
    touch-action: pan-y;
    user-select: none;
    -webkit-user-select: none;
    -webkit-touch-callout: none;
}

.swipe-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    background: #000;
    z-index: 1;
}

.swipe-item {
    position: absolute;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    display: none;
    opacity: 0;
    transform: translateY(100%);
    transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94), opacity 0.3s ease;
    will-change: transform, opacity;
}

.swipe-item.active {
    display: flex;
    opacity: 1;
    transform: translateY(0);
}

.swipe-item.prev {
    transform: translateY(-100%);
}

.swipe-item.next {
    transform: translateY(100%);
}

.media-container {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #000;
}

.media-content {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    background: #000;
}

/* TikTok-style UI Overlays */
.tiktok-ui {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 10;
}

.tiktok-sidebar {
    position: absolute;
    right: 12px;
    bottom: 100px;
    display: flex;
    flex-direction: column;
    gap: 20px;
    pointer-events: auto;
}

.tiktok-action-btn {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    border: none;
    color: white;
    font-size: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.tiktok-action-btn:active {
    transform: scale(0.95);
    background: rgba(255, 255, 255, 0.3);
}

.tiktok-bottom-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 60px;
    padding: 20px;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    pointer-events: auto;
}

.creator-info {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
}

.creator-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 16px;
    color: white;
    border: 2px solid white;
}

.creator-name {
    font-weight: 600;
    font-size: 16px;
    color: white;
}

.media-description {
    color: white;
    font-size: 14px;
    line-height: 1.4;
    margin-bottom: 8px;
    max-height: 60px;
    overflow: hidden;
}

/* Loading States */
.media-loading-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 20;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid #fff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Video Controls */
.video-content {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80px;
    height: 80px;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(10px);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 15;
    transition: all 0.2s ease;
    border: 2px solid rgba(255, 255, 255, 0.8);
}

.play-button:active {
    transform: translate(-50%, -50%) scale(0.95);
}

.play-button i {
    color: white;
    font-size: 32px;
    margin-left: 4px;
}

.video-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 2px;
    background: rgba(255, 255, 255, 0.8);
    transform-origin: left;
    transform: scaleX(0);
    transition: transform 0.1s linear;
    z-index: 15;
}

/* Error States */
.media-error {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    color: #fff;
    padding: 20px;
    border-radius: 12px;
    font-size: 16px;
    z-index: 25;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Progress Indicator */
.progress-indicator {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 4px;
    z-index: 20;
}

.progress-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.4);
    transition: all 0.3s ease;
}

.progress-dot.active {
    background: white;
    transform: scale(1.2);
}


/* Swipe Gestures & Animations */
.swipe-item.swiping {
    transition: none;
}

.swipe-item.snap-back {
    transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Metadata Toggle */
.metadata-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 60px;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: white;
    padding: 20px;
    z-index: 12;
    pointer-events: auto;
}

.metadata-toggle {
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    margin-top: 12px;
    font-size: 14px;
    transition: all 0.2s ease;
}

.metadata-toggle:active {
    transform: scale(0.95);
    background: rgba(255, 255, 255, 0.3);
}

.metadata-container {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.metadata-container.visible {
    max-height: 200px;
    margin-top: 12px;
    padding: 12px;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.metadata-item {
    margin-bottom: 8px;
    font-size: 13px;
    line-height: 1.4;
}

.metadata-item strong {
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

/* Loading States */
.loading-indicator {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
    pointer-events: none;
}

/* Responsive Design */
@media (max-width: 480px) {
    .tiktok-sidebar {
        right: 8px;
        bottom: 80px;
        gap: 16px;
    }

    .tiktok-action-btn {
        width: 44px;
        height: 44px;
        font-size: 18px;
    }

    .tiktok-bottom-info {
        padding: 16px;
        right: 52px;
    }

    .creator-avatar {
        width: 36px;
        height: 36px;
        font-size: 14px;
    }

    .creator-name {
        font-size: 15px;
    }

    .media-description {
        font-size: 13px;
    }
}

/* Safe Area Support for iPhone X+ */
@supports (padding: max(0px)) {
    .tiktok-bottom-info {
        padding-bottom: max(20px, env(safe-area-inset-bottom));
    }

    .tiktok-sidebar {
        bottom: max(100px, calc(100px + env(safe-area-inset-bottom)));
    }
}

/* Empty State */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    text-align: center;
    color: white;
    padding: 20px;
}

.empty-icon {
    font-size: 64px;
    margin-bottom: 20px;
    opacity: 0.7;
}

.empty-title {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 10px;
    opacity: 0.9;
}

.empty-subtitle {
    font-size: 16px;
    opacity: 0.7;
    line-height: 1.4;
}

/* Debug Info */
.ultrafast-debug {
    position: fixed;
    top: 10px;
    right: 10px;
    background: rgba(0,0,0,0.8);
    color: #00ff00;
    padding: 8px;
    border-radius: 4px;
    font-family: monospace;
    font-size: 12px;
    z-index: 1000;
    min-width: 120px;
}
